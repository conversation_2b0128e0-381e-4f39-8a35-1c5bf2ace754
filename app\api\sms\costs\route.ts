import { NextRequest, NextResponse } from 'next/server'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { prisma } from '@/lib/db'
import { SMSCostService } from '@/lib/services/sms-cost'

/**
 * GET /api/sms/costs
 * Get SMS cost tracking and usage reports
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'sms-costs', 50, 60 * 1000) // 50 requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse query parameters
    const url = new URL(request.url)
    const reportType = url.searchParams.get('type') || 'summary'
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')

    // Default to last 30 days if no date range provided
    const end = endDate ? new Date(endDate) : new Date()
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - (30 * 24 * 60 * 60 * 1000))

    switch (reportType) {
      case 'summary':
        // Get basic SMS statistics
        const stats = await SMSCostService.getSMSStats(start, end)
        const currentMonthSpending = await SMSCostService.getCurrentMonthSpending()
        const budgetAlerts = await SMSCostService.getBudgetAlerts()

        return NextResponse.json({
          success: true,
          reportType: 'summary',
          period: { start, end },
          stats,
          currentMonth: currentMonthSpending,
          budgetAlerts
        })

      case 'daily':
        // Get daily cost breakdown
        const dailyCosts = await SMSCostService.getDailyCosts(start, end)
        
        return NextResponse.json({
          success: true,
          reportType: 'daily',
          period: { start, end },
          dailyCosts,
          total: dailyCosts.reduce((sum, day) => sum + day.totalCost, 0),
          totalMessages: dailyCosts.reduce((sum, day) => sum + day.totalSent, 0)
        })

      case 'breakdown':
        // Get cost breakdown by category
        const costBreakdown = await SMSCostService.getCostBreakdown(start, end)
        
        return NextResponse.json({
          success: true,
          reportType: 'breakdown',
          period: { start, end },
          breakdown: costBreakdown,
          total: costBreakdown.reduce((sum, item) => sum + item.cost, 0)
        })

      case 'failed':
        // Get failed messages cost analysis
        const failedAnalysis = await SMSCostService.getFailedMessagesCost(start, end)
        
        return NextResponse.json({
          success: true,
          reportType: 'failed',
          period: { start, end },
          failedAnalysis
        })

      case 'usage':
        // Get comprehensive usage report
        const usageReport = await SMSCostService.getUsageReport(start, end)
        
        return NextResponse.json({
          success: true,
          reportType: 'usage',
          usageReport
        })

      case 'budget':
        // Get budget status and alerts
        const dailyBudget = await SMSCostService.checkDailyBudgetLimit()
        const monthlySpending = await SMSCostService.getCurrentMonthSpending()
        const alerts = await SMSCostService.getBudgetAlerts()

        return NextResponse.json({
          success: true,
          reportType: 'budget',
          dailyBudget,
          monthlySpending,
          alerts
        })

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid report type. Available types: summary, daily, breakdown, failed, usage, budget'
          },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Get SMS costs error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/sms/costs
 * Update SMS cost settings (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Only admins can update cost settings
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { settings } = body

    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { success: false, error: 'Settings object is required' },
        { status: 400 }
      )
    }

    // Validate and update settings
    const validSettings = [
      'daily_budget_limit',
      'monthly_budget_limit',
      'cost_per_sms',
      'max_retries',
      'retry_delay',
      'rate_limit_per_minute',
      'enable_cost_tracking',
      'enable_budget_alerts',
      'budget_alert_threshold'
    ]

    const updatedSettings = []

    for (const [key, value] of Object.entries(settings)) {
      if (!validSettings.includes(key)) {
        return NextResponse.json(
          { success: false, error: `Invalid setting key: ${key}` },
          { status: 400 }
        )
      }

      // Validate value based on setting type
      if (key.includes('budget_limit') || key === 'cost_per_sms') {
        const numValue = parseFloat(value as string)
        if (isNaN(numValue) || numValue < 0) {
          return NextResponse.json(
            { success: false, error: `Invalid value for ${key}: must be a positive number` },
            { status: 400 }
          )
        }
      }

      if (key === 'max_retries' || key === 'retry_delay' || key === 'rate_limit_per_minute' || key === 'budget_alert_threshold') {
        const numValue = parseInt(value as string)
        if (isNaN(numValue) || numValue < 0) {
          return NextResponse.json(
            { success: false, error: `Invalid value for ${key}: must be a positive integer` },
            { status: 400 }
          )
        }
      }

      if (key.startsWith('enable_')) {
        if (typeof value !== 'boolean' && value !== 'true' && value !== 'false') {
          return NextResponse.json(
            { success: false, error: `Invalid value for ${key}: must be boolean` },
            { status: 400 }
          )
        }
      }

      // Update setting
      await SMSCostService.updateSMSSetting(
        key,
        value.toString(),
        undefined,
        user.id
      )

      updatedSettings.push(key)
    }

    return NextResponse.json({
      success: true,
      message: `Updated ${updatedSettings.length} settings`,
      updatedSettings
    })

  } catch (error) {
    console.error('Update SMS cost settings error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/sms/costs
 * Reset cost tracking data (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Only admins can reset cost data
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { action, olderThanDays } = body

    if (action === 'reset_daily_costs') {
      const days = olderThanDays || 90
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      // Delete old daily cost records
      const deleteResult = await prisma.sMSCost.deleteMany({
        where: {
          date: {
            lt: cutoffDate
          }
        }
      })

      return NextResponse.json({
        success: true,
        message: `Deleted ${deleteResult.count} daily cost records older than ${days} days`,
        deletedCount: deleteResult.count
      })

    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action. Available actions: reset_daily_costs' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Reset SMS costs error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET for cost reports, POST to update settings, or PUT to reset data.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET for cost reports, POST to update settings, or PUT to reset data.' },
    { status: 405 }
  )
}
