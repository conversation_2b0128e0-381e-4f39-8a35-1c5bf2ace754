import { SMSStatus, SMSType, SMSPriority, TemplateCategory } from '../../generated/prisma'

// Core SMS interfaces
export interface SMSMessage {
  id: string
  recipientNumber: string
  message: string
  status: SMSStatus
  type: SMSType
  priority: SMSPriority
  sentAt?: Date
  deliveredAt?: Date
  failedAt?: Date
  scheduledFor?: Date
  retryCount: number
  maxRetries: number
  cost?: number
  messageId?: string
  errorMessage?: string
  studentId?: string
  attendanceId?: string
  templateId?: string
  batchId?: string
  userId?: string
  createdAt: Date
  updatedAt: Date
}

// SMS Template interfaces
export interface SMSTemplate {
  id: string
  name: string
  category: TemplateCategory
  subject?: string
  content: string
  variables: string[]
  isActive: boolean
  isSystem: boolean
  description?: string
  createdBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateSMSTemplateInput {
  name: string
  category: TemplateCategory
  subject?: string
  content: string
  variables: string[]
  description?: string
}

export interface UpdateSMSTemplateInput {
  name?: string
  category?: TemplateCategory
  subject?: string
  content?: string
  variables?: string[]
  description?: string
  isActive?: boolean
}

// SMS sending interfaces
export interface SendSMSInput {
  recipientNumber: string
  message: string
  type?: SMSType
  priority?: SMSPriority
  scheduledFor?: Date
  studentId?: string
  attendanceId?: string
  templateId?: string
}

export interface SendBatchSMSInput {
  recipients: Array<{
    recipientNumber: string
    message: string
    studentId?: string
    attendanceId?: string
  }>
  type?: SMSType
  priority?: SMSPriority
  scheduledFor?: Date
  templateId?: string
}

export interface SendTemplatedSMSInput {
  templateId: string
  recipientNumber: string
  variables: Record<string, string>
  type?: SMSType
  priority?: SMSPriority
  scheduledFor?: Date
  studentId?: string
  attendanceId?: string
}

// SMS response interfaces
export interface SMSResponse {
  success: boolean
  messageId?: string
  smsLogId?: string
  cost?: number
  error?: string
  details?: any
}

export interface BatchSMSResponse {
  success: boolean
  batchId?: string
  totalMessages: number
  successCount: number
  failedCount: number
  results: Array<{
    recipientNumber: string
    success: boolean
    messageId?: string
    smsLogId?: string
    error?: string
  }>
  totalCost?: number
}

// SMS status and tracking interfaces
export interface SMSStatusResponse {
  found: boolean
  status?: SMSStatus
  sentAt?: Date
  deliveredAt?: Date
  failedAt?: Date
  cost?: number
  errorMessage?: string
}

export interface SMSDeliveryStatus {
  messageId: string
  status: 'delivered' | 'failed' | 'pending'
  deliveredAt?: Date
  failureReason?: string
}

// SMS statistics interfaces
export interface SMSStats {
  total: number
  pending: number
  queued: number
  sent: number
  delivered: number
  failed: number
  cancelled: number
  deliveryRate: number
  successRate: number
  totalCost: number
  avgCostPerSMS: number
}

export interface SMSCostSummary {
  date: Date
  totalSent: number
  totalCost: number
  avgCostPerSMS: number
}

export interface SMSUsageReport {
  period: {
    start: Date
    end: Date
  }
  stats: SMSStats
  dailyCosts: SMSCostSummary[]
  topRecipients: Array<{
    recipientNumber: string
    count: number
    cost: number
  }>
  messageTypes: Array<{
    type: SMSType
    count: number
    cost: number
  }>
}

// SMS blacklist interfaces
export interface SMSBlacklistEntry {
  id: string
  phoneNumber: string
  reason?: string
  addedBy?: string
  createdAt: Date
}

export interface CreateBlacklistEntryInput {
  phoneNumber: string
  reason?: string
}

// SMS settings interfaces
export interface SMSSetting {
  id: string
  key: string
  value: string
  description?: string
  isEncrypted: boolean
  updatedBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface SMSConfiguration {
  semaphoreApiKey: string
  semaphoreBaseUrl: string
  defaultSenderName: string
  maxRetries: number
  retryDelay: number
  rateLimitPerMinute: number
  costPerSMS: number
  dailyBudgetLimit: number
  enableScheduling: boolean
  enableDeliveryReceipts: boolean
  enableCostTracking: boolean
  defaultPriority: SMSPriority
}

// Queue and processing interfaces
export interface SMSQueueItem {
  id: string
  smsLogId: string
  priority: SMSPriority
  scheduledFor?: Date
  retryCount: number
  createdAt: Date
}

export interface SMSProcessingResult {
  smsLogId: string
  success: boolean
  messageId?: string
  cost?: number
  error?: string
  shouldRetry: boolean
}

// Validation interfaces
export interface PhoneNumberValidation {
  isValid: boolean
  formatted?: string
  country?: string
  carrier?: string
  type?: 'mobile' | 'landline' | 'unknown'
}

export interface SMSValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Template variable interfaces
export interface TemplateVariable {
  name: string
  description: string
  example: string
  required: boolean
}

export interface TemplateVariables {
  student: TemplateVariable[]
  attendance: TemplateVariable[]
  school: TemplateVariable[]
  general: TemplateVariable[]
}

// Error interfaces
export interface SMSError {
  code: string
  message: string
  details?: any
  retryable: boolean
}

// Semaphore API specific interfaces
export interface SemaphoreAPIResponse {
  message_id?: number
  user_id?: number
  user?: string
  account_id?: number
  account?: string
  recipient?: string
  message?: string
  sender_name?: string
  network?: string
  status?: string
  type?: string
  source?: string
  created_at?: string
  updated_at?: string
}

export interface SemaphoreErrorResponse {
  error?: string
  message?: string
  code?: number
}

export interface SemaphoreBalanceResponse {
  user_id?: number
  user?: string
  account_id?: number
  account?: string
  balance?: number
  currency?: string
}
