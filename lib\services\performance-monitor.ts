import { prisma } from '@/lib/db'
import { reportsConfig } from '@/lib/config/reports'

export interface PerformanceMetrics {
  timestamp: Date
  operation: string
  duration: number
  memoryUsage: number
  cpuUsage?: number
  success: boolean
  error?: string
  metadata?: Record<string, any>
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  uptime: number
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  diskUsage: {
    used: number
    total: number
    percentage: number
  }
  activeReports: number
  queuedReports: number
  cacheHitRate: number
  averageResponseTime: number
  errorRate: number
}

export interface PerformanceAlert {
  id: string
  type: 'memory' | 'disk' | 'response_time' | 'error_rate' | 'queue_size'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  threshold: number
  currentValue: number
  timestamp: Date
  resolved: boolean
}

export class PerformanceMonitorService {
  private static instance: PerformanceMonitorService
  private metrics: PerformanceMetrics[] = []
  private alerts: PerformanceAlert[] = []
  private startTime: Date = new Date()
  private isMonitoring: boolean = false

  private constructor() {}

  static getInstance(): PerformanceMonitorService {
    if (!PerformanceMonitorService.instance) {
      PerformanceMonitorService.instance = new PerformanceMonitorService()
    }
    return PerformanceMonitorService.instance
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.startTime = new Date()

    // Start periodic health checks
    setInterval(() => {
      this.performHealthCheck()
    }, 60000) // Every minute

    // Start metrics cleanup
    setInterval(() => {
      this.cleanupOldMetrics()
    }, 300000) // Every 5 minutes

    console.log('Performance monitoring started')
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false
    console.log('Performance monitoring stopped')
  }

  /**
   * Record a performance metric
   */
  recordMetric(
    operation: string,
    startTime: Date,
    success: boolean,
    error?: string,
    metadata?: Record<string, any>
  ): void {
    if (!reportsConfig.performance.enableMetrics) return

    const duration = Date.now() - startTime.getTime()
    const memoryUsage = process.memoryUsage()

    const metric: PerformanceMetrics = {
      timestamp: new Date(),
      operation,
      duration,
      memoryUsage: memoryUsage.heapUsed,
      success,
      error,
      metadata
    }

    this.metrics.push(metric)

    // Check for performance alerts
    this.checkPerformanceThresholds(metric)

    // Keep only recent metrics in memory
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500)
    }
  }

  /**
   * Get current system health
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const memoryUsage = process.memoryUsage()
    const uptime = Date.now() - this.startTime.getTime()

    // Get active and queued reports
    const [activeReports, queuedReports] = await Promise.all([
      prisma.generatedReport.count({
        where: { status: 'GENERATING' }
      }),
      prisma.generatedReport.count({
        where: { status: 'PENDING' }
      })
    ])

    // Calculate cache hit rate
    const cacheHitRate = await this.calculateCacheHitRate()

    // Calculate average response time
    const recentMetrics = this.metrics.filter(m => 
      Date.now() - m.timestamp.getTime() < 300000 // Last 5 minutes
    )
    const averageResponseTime = recentMetrics.length > 0
      ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length
      : 0

    // Calculate error rate
    const errorRate = recentMetrics.length > 0
      ? (recentMetrics.filter(m => !m.success).length / recentMetrics.length) * 100
      : 0

    // Determine system status
    let status: SystemHealth['status'] = 'healthy'
    const memoryPercentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100

    if (memoryPercentage > 90 || errorRate > 10 || averageResponseTime > 10000) {
      status = 'critical'
    } else if (memoryPercentage > 75 || errorRate > 5 || averageResponseTime > 5000) {
      status = 'warning'
    }

    return {
      status,
      uptime,
      memoryUsage: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: memoryPercentage
      },
      diskUsage: await this.getDiskUsage(),
      activeReports,
      queuedReports,
      cacheHitRate,
      averageResponseTime,
      errorRate
    }
  }

  /**
   * Get performance metrics for a time period
   */
  getMetrics(
    operation?: string,
    startTime?: Date,
    endTime?: Date
  ): PerformanceMetrics[] {
    let filteredMetrics = [...this.metrics]

    if (operation) {
      filteredMetrics = filteredMetrics.filter(m => m.operation === operation)
    }

    if (startTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp >= startTime)
    }

    if (endTime) {
      filteredMetrics = filteredMetrics.filter(m => m.timestamp <= endTime)
    }

    return filteredMetrics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  /**
   * Get current alerts
   */
  getAlerts(resolved: boolean = false): PerformanceAlert[] {
    return this.alerts.filter(alert => alert.resolved === resolved)
  }

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.resolved = true
    }
  }

  /**
   * Get performance statistics
   */
  getStatistics(operation?: string): {
    totalOperations: number
    successRate: number
    averageDuration: number
    minDuration: number
    maxDuration: number
    averageMemoryUsage: number
  } {
    const metrics = operation 
      ? this.metrics.filter(m => m.operation === operation)
      : this.metrics

    if (metrics.length === 0) {
      return {
        totalOperations: 0,
        successRate: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        averageMemoryUsage: 0
      }
    }

    const successfulOperations = metrics.filter(m => m.success).length
    const durations = metrics.map(m => m.duration)
    const memoryUsages = metrics.map(m => m.memoryUsage)

    return {
      totalOperations: metrics.length,
      successRate: (successfulOperations / metrics.length) * 100,
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      averageMemoryUsage: memoryUsages.reduce((sum, m) => sum + m, 0) / memoryUsages.length
    }
  }

  // Private helper methods
  private async performHealthCheck(): Promise<void> {
    try {
      const health = await this.getSystemHealth()

      // Check memory usage
      if (health.memoryUsage.percentage > 90) {
        this.createAlert('memory', 'critical', 
          `Memory usage is critically high: ${health.memoryUsage.percentage.toFixed(1)}%`,
          90, health.memoryUsage.percentage)
      } else if (health.memoryUsage.percentage > 75) {
        this.createAlert('memory', 'high',
          `Memory usage is high: ${health.memoryUsage.percentage.toFixed(1)}%`,
          75, health.memoryUsage.percentage)
      }

      // Check disk usage
      if (health.diskUsage.percentage > 90) {
        this.createAlert('disk', 'critical',
          `Disk usage is critically high: ${health.diskUsage.percentage.toFixed(1)}%`,
          90, health.diskUsage.percentage)
      }

      // Check response time
      if (health.averageResponseTime > 10000) {
        this.createAlert('response_time', 'high',
          `Average response time is high: ${health.averageResponseTime.toFixed(0)}ms`,
          10000, health.averageResponseTime)
      }

      // Check error rate
      if (health.errorRate > 10) {
        this.createAlert('error_rate', 'high',
          `Error rate is high: ${health.errorRate.toFixed(1)}%`,
          10, health.errorRate)
      }

      // Check queue size
      if (health.queuedReports > 50) {
        this.createAlert('queue_size', 'medium',
          `Report queue is large: ${health.queuedReports} reports`,
          50, health.queuedReports)
      }

    } catch (error) {
      console.error('Health check failed:', error)
    }
  }

  private createAlert(
    type: PerformanceAlert['type'],
    severity: PerformanceAlert['severity'],
    message: string,
    threshold: number,
    currentValue: number
  ): void {
    // Check if similar alert already exists and is not resolved
    const existingAlert = this.alerts.find(alert =>
      alert.type === type &&
      alert.severity === severity &&
      !alert.resolved &&
      Date.now() - alert.timestamp.getTime() < 300000 // Within 5 minutes
    )

    if (existingAlert) return

    const alert: PerformanceAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      message,
      threshold,
      currentValue,
      timestamp: new Date(),
      resolved: false
    }

    this.alerts.push(alert)

    // Keep only recent alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-50)
    }

    console.warn(`Performance Alert [${severity.toUpperCase()}]: ${message}`)
  }

  private checkPerformanceThresholds(metric: PerformanceMetrics): void {
    // Check response time
    if (metric.duration > 30000) { // 30 seconds
      this.createAlert('response_time', 'critical',
        `Operation ${metric.operation} took ${metric.duration}ms`,
        30000, metric.duration)
    } else if (metric.duration > 10000) { // 10 seconds
      this.createAlert('response_time', 'high',
        `Operation ${metric.operation} took ${metric.duration}ms`,
        10000, metric.duration)
    }

    // Check memory usage
    const memoryMB = metric.memoryUsage / (1024 * 1024)
    if (memoryMB > reportsConfig.performance.maxMemoryUsage) {
      this.createAlert('memory', 'high',
        `Operation ${metric.operation} used ${memoryMB.toFixed(1)}MB memory`,
        reportsConfig.performance.maxMemoryUsage, memoryMB)
    }
  }

  private cleanupOldMetrics(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000) // 24 hours ago
    this.metrics = this.metrics.filter(metric => 
      metric.timestamp.getTime() > cutoffTime
    )

    // Cleanup old alerts
    this.alerts = this.alerts.filter(alert =>
      Date.now() - alert.timestamp.getTime() < (7 * 24 * 60 * 60 * 1000) // 7 days
    )
  }

  private async calculateCacheHitRate(): Promise<number> {
    try {
      // This would integrate with your cache service
      // For now, return a placeholder value
      return 85.5
    } catch (error) {
      return 0
    }
  }

  private async getDiskUsage(): Promise<{ used: number; total: number; percentage: number }> {
    try {
      // This would check actual disk usage
      // For now, return placeholder values
      return {
        used: 50 * 1024 * 1024 * 1024, // 50GB
        total: 100 * 1024 * 1024 * 1024, // 100GB
        percentage: 50
      }
    } catch (error) {
      return { used: 0, total: 0, percentage: 0 }
    }
  }
}

// Performance monitoring decorator
export function monitorPerformance(operation: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const monitor = PerformanceMonitorService.getInstance()
      const startTime = new Date()
      let success = true
      let error: string | undefined

      try {
        const result = await method.apply(this, args)
        return result
      } catch (err) {
        success = false
        error = err instanceof Error ? err.message : 'Unknown error'
        throw err
      } finally {
        monitor.recordMetric(operation, startTime, success, error, {
          method: propertyName,
          args: args.length
        })
      }
    }

    return descriptor
  }
}
