# SMS Notification System Documentation

## Overview

The SMS Notification System is a comprehensive solution for sending SMS notifications to parents and guardians using the Semaphore SMS gateway. It provides template-based messaging, batch processing, delivery tracking, cost monitoring, and scheduling capabilities.

## Features

### Core Features
- ✅ **Semaphore API Integration** - Direct integration with Semaphore SMS gateway
- ✅ **Template-based Messaging** - Reusable message templates with variable substitution
- ✅ **Batch SMS Processing** - Send multiple SMS messages efficiently
- ✅ **Delivery Status Tracking** - Real-time delivery status monitoring
- ✅ **Cost Monitoring** - Track SMS costs and budget limits
- ✅ **Queue System** - Reliable message queuing with retry mechanism
- ✅ **SMS Scheduling** - Schedule messages for future delivery
- ✅ **Blacklist Management** - Manage opt-outs and blocked numbers
- ✅ **Rate Limiting** - Prevent spam and respect API limits
- ✅ **Comprehensive Logging** - Detailed SMS history and audit trails

### Advanced Features
- ✅ **Budget Alerts** - Automatic alerts when approaching budget limits
- ✅ **Retry Mechanism** - Automatic retry for failed messages
- ✅ **Phone Number Validation** - Philippine phone number format validation
- ✅ **Template Validation** - Ensure template integrity and variable usage
- ✅ **Cost Calculation** - Accurate cost tracking per message
- ✅ **Recurring Schedules** - Cron-like scheduling for recurring messages

## API Endpoints

### SMS Sending
- `POST /api/sms/send` - Send individual SMS
- `POST /api/sms/batch` - Send batch SMS notifications

### Status and Tracking
- `GET /api/sms/status/[id]` - Check SMS delivery status
- `GET /api/sms/logs` - SMS history and logs

### Templates
- `GET /api/sms/templates` - Get message templates
- `POST /api/sms/templates` - Create/update templates

### Cost Management
- `GET /api/sms/costs` - SMS cost tracking and reports
- `POST /api/sms/costs` - Update cost settings

### Testing and Configuration
- `POST /api/sms/test` - Test SMS configuration
- `GET /api/sms/test` - Get available test types

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
# SMS Configuration (Semaphore API)
SEMAPHORE_API_KEY="your-semaphore-api-key-here"
SEMAPHORE_BASE_URL="https://api.semaphore.co"
SEMAPHORE_SENDER_NAME="TSAT"
SMS_COST_PER_MESSAGE="2.50"
```

### Database Setup

The system extends the existing database schema with new models:

1. **Enhanced SMSLog** - Extended with priority, scheduling, and cost tracking
2. **SMSTemplate** - Message templates with variable support
3. **SMSBlacklist** - Blocked phone numbers
4. **SMSCost** - Daily cost tracking
5. **SMSSetting** - System configuration

Run the database migration:
```bash
npm run db:push
```

## Usage Examples

### 1. Send Individual SMS

```typescript
import { SMSService } from '@/lib/services/sms-service'

const smsService = SMSService.getInstance()

const result = await smsService.sendSMS({
  recipientNumber: '09123456789',
  message: 'Hello! Your child has arrived at school.',
  type: 'ATTENDANCE',
  priority: 'NORMAL'
}, userId)

if (result.success) {
  console.log('SMS sent successfully:', result.messageId)
} else {
  console.error('SMS failed:', result.error)
}
```

### 2. Send Batch SMS

```typescript
const batchResult = await smsService.sendBatchSMS({
  recipients: [
    {
      recipientNumber: '09123456789',
      message: 'Student John arrived at school at 7:30 AM',
      studentId: 'student-id-1'
    },
    {
      recipientNumber: '09987654321',
      message: 'Student Jane arrived at school at 7:35 AM',
      studentId: 'student-id-2'
    }
  ],
  type: 'ATTENDANCE',
  priority: 'NORMAL'
}, userId)

console.log(`Batch completed: ${batchResult.successCount} sent, ${batchResult.failedCount} failed`)
```

### 3. Use Templates

```typescript
// Create a template
const template = await SMSTemplateService.createTemplate({
  name: 'Student Arrival',
  category: 'ATTENDANCE',
  content: 'Good day {guardianName}! Your child {studentName} has arrived at school at {timeIn}.',
  variables: ['guardianName', 'studentName', 'timeIn'],
  description: 'Notification for student arrival'
}, userId)

// Send templated SMS
const result = await smsService.sendTemplatedSMS({
  templateId: template.id,
  recipientNumber: '09123456789',
  variables: {
    guardianName: 'Mrs. Santos',
    studentName: 'John Santos',
    timeIn: '7:30 AM'
  },
  type: 'ATTENDANCE'
}, userId)
```

### 4. Schedule SMS

```typescript
const scheduledDate = new Date()
scheduledDate.setHours(scheduledDate.getHours() + 2) // 2 hours from now

const result = await smsService.scheduleSMS({
  recipientNumber: '09123456789',
  message: 'Reminder: Parent-Teacher Conference tomorrow at 2 PM',
  scheduledFor: scheduledDate,
  name: 'PTC Reminder',
  description: 'Parent-Teacher Conference reminder',
  type: 'REMINDER'
}, userId)
```

### 5. Manage Blacklist

```typescript
import { SMSBlacklistService } from '@/lib/services/sms-blacklist'

// Add to blacklist
await SMSBlacklistService.addToBlacklist({
  phoneNumber: '09123456789',
  reason: 'User requested opt-out'
}, userId)

// Check if blacklisted
const isBlacklisted = await SMSBlacklistService.isBlacklisted('09123456789')

// Remove from blacklist
await SMSBlacklistService.removeFromBlacklist('09123456789')
```

## System Initialization

Initialize the SMS system in your application startup:

```typescript
import { SMSService } from '@/lib/services/sms-service'

// Initialize SMS system
const smsService = SMSService.getInstance()
await smsService.initialize()

// Graceful shutdown
process.on('SIGTERM', () => {
  smsService.shutdown()
})
```

## Monitoring and Maintenance

### Health Check

```typescript
const healthStatus = await smsService.healthCheck()
console.log('SMS System Status:', healthStatus.status)
console.log('Checks:', healthStatus.checks)
```

### System Status

```typescript
const status = await smsService.getSystemStatus()
console.log('Queue Status:', status.queue)
console.log('Budget Status:', status.budget)
console.log('Semaphore Status:', status.semaphore)
```

### Cost Monitoring

```typescript
import { SMSCostService } from '@/lib/services/sms-cost'

// Get current month spending
const monthlySpending = await SMSCostService.getCurrentMonthSpending()

// Check budget alerts
const alerts = await SMSCostService.getBudgetAlerts()

// Get usage report
const report = await SMSCostService.getUsageReport(startDate, endDate)
```

## Best Practices

### 1. Message Content
- Keep messages concise and clear
- Use templates for consistent messaging
- Include school name for identification
- Provide contact information for inquiries

### 2. Timing
- Send messages during appropriate hours (7 AM - 8 PM)
- Use scheduling for non-urgent messages
- Respect parent preferences for timing

### 3. Cost Management
- Set appropriate daily and monthly budget limits
- Monitor costs regularly
- Use batch sending for efficiency
- Implement approval workflows for large batches

### 4. Privacy and Compliance
- Respect opt-out requests immediately
- Maintain blacklist properly
- Log all SMS activities for audit
- Secure API keys and sensitive data

### 5. Error Handling
- Implement proper retry mechanisms
- Monitor failed messages
- Validate phone numbers before sending
- Handle API rate limits gracefully

## Troubleshooting

### Common Issues

1. **SMS Not Sending**
   - Check Semaphore API key configuration
   - Verify phone number format
   - Check daily budget limits
   - Ensure recipient is not blacklisted

2. **High Costs**
   - Review message length (160 chars = 1 SMS)
   - Check for duplicate sends
   - Monitor batch operations
   - Verify cost per SMS setting

3. **Delivery Issues**
   - Check network connectivity
   - Verify recipient phone number
   - Monitor Semaphore service status
   - Review delivery status logs

4. **Queue Problems**
   - Check queue processing status
   - Monitor failed message retries
   - Verify database connectivity
   - Review error logs

## Support

For technical support or questions:
- Check the system logs for detailed error messages
- Use the test endpoints to verify configuration
- Monitor the health check endpoint
- Review the SMS logs for delivery status

## Testing

A comprehensive test suite is available to verify SMS functionality:

```bash
# Run SMS system tests
npm test -- --testPathPattern=sms

# Test specific components
npm test -- sms-service.test.ts
npm test -- semaphore.test.ts
npm test -- sms-template.test.ts
```

## Security Considerations

- Store API keys securely in environment variables
- Implement proper authentication for SMS endpoints
- Use rate limiting to prevent abuse
- Audit all SMS activities
- Encrypt sensitive configuration data
- Regularly rotate API keys
- Monitor for unusual sending patterns
