import { prisma } from '@/lib/db'
import { AttendanceStatus, StudentStatus } from '../../generated/prisma'
import type {
  AttendanceQueryInput,
  QRValidationResultInput,
  AttendanceAnalyticsInput
} from '@/lib/validations/attendance'

// Database utility functions for attendance
export const attendanceDbUtils = {
  // Build where clause for attendance queries
  buildWhereClause: (query: AttendanceQueryInput) => {
    const where: any = {}

    // Date filters
    if (query.date) {
      const startOfDay = new Date(query.date)
      startOfDay.setHours(0, 0, 0, 0)
      const endOfDay = new Date(query.date)
      endOfDay.setHours(23, 59, 59, 999)
      
      where.date = {
        gte: startOfDay,
        lte: endOfDay
      }
    } else if (query.startDate || query.endDate) {
      where.date = {}
      if (query.startDate) {
        where.date.gte = query.startDate
      }
      if (query.endDate) {
        where.date.lte = query.endDate
      }
    }

    // Student filters
    if (query.studentId) {
      where.studentId = query.studentId
    }

    // Teacher filter
    if (query.teacherId) {
      where.teacherId = query.teacherId
    }

    // Status filter
    if (query.status) {
      where.status = query.status
    }

    // Grade level and section filters (through student relation)
    if (query.gradeLevel || query.section || query.search) {
      where.student = {}
      
      if (query.gradeLevel) {
        where.student.gradeLevel = query.gradeLevel
      }
      
      if (query.section) {
        where.student.section = query.section
      }
      
      if (query.search) {
        where.student.OR = [
          { firstName: { contains: query.search, mode: 'insensitive' } },
          { lastName: { contains: query.search, mode: 'insensitive' } },
          { studentNumber: { contains: query.search, mode: 'insensitive' } }
        ]
      }
    }

    return where
  },

  // Build include clause for attendance queries
  buildIncludeClause: (query: AttendanceQueryInput) => {
    const include: any = {}

    if (query.includeStudent) {
      include.student = {
        select: {
          id: true,
          studentNumber: true,
          firstName: true,
          lastName: true,
          middleName: true,
          gradeLevel: true,
          section: true,
          status: true
        }
      }
    }

    if (query.includeTeacher) {
      include.teacher = {
        select: {
          id: true,
          employeeNumber: true,
          firstName: true,
          lastName: true,
          middleName: true
        }
      }
    }

    if (query.includeSMSLogs) {
      include.smsLogs = {
        select: {
          id: true,
          recipientNumber: true,
          message: true,
          status: true,
          sentAt: true,
          deliveredAt: true
        }
      }
    }

    return include
  },

  // Build order by clause for attendance queries
  buildOrderByClause: (query: AttendanceQueryInput) => {
    const orderBy: any = {}

    switch (query.sortBy) {
      case 'studentName':
        return [
          { student: { lastName: query.sortOrder } },
          { student: { firstName: query.sortOrder } }
        ]
      case 'date':
        orderBy.date = query.sortOrder
        break
      case 'timeIn':
        orderBy.timeIn = query.sortOrder
        break
      case 'timeOut':
        orderBy.timeOut = query.sortOrder
        break
      case 'status':
        orderBy.status = query.sortOrder
        break
      case 'createdAt':
        orderBy.createdAt = query.sortOrder
        break
      default:
        orderBy.date = query.sortOrder
    }

    return orderBy
  },

  // Check if attendance already exists for student on date
  checkAttendanceExists: async (studentId: string, date: Date) => {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const existing = await prisma.attendance.findFirst({
      where: {
        studentId,
        date: {
          gte: startOfDay,
          lte: endOfDay
        }
      }
    })

    return existing
  },

  // Get attendance statistics for a date range
  getAttendanceStats: async (startDate: Date, endDate: Date, filters?: {
    gradeLevel?: string
    section?: string
  }) => {
    const where: any = {
      date: {
        gte: startDate,
        lte: endDate
      }
    }

    if (filters?.gradeLevel || filters?.section) {
      where.student = {}
      if (filters.gradeLevel) {
        where.student.gradeLevel = filters.gradeLevel
      }
      if (filters.section) {
        where.student.section = filters.section
      }
    }

    const stats = await prisma.attendance.groupBy({
      by: ['status'],
      where,
      _count: {
        status: true
      }
    })

    const result = {
      total: 0,
      present: 0,
      absent: 0,
      late: 0,
      excused: 0,
      attendanceRate: 0
    }

    stats.forEach(stat => {
      result.total += stat._count.status
      switch (stat.status) {
        case AttendanceStatus.PRESENT:
          result.present = stat._count.status
          break
        case AttendanceStatus.ABSENT:
          result.absent = stat._count.status
          break
        case AttendanceStatus.LATE:
          result.late = stat._count.status
          break
        case AttendanceStatus.EXCUSED:
          result.excused = stat._count.status
          break
      }
    })

    if (result.total > 0) {
      result.attendanceRate = ((result.present + result.late + result.excused) / result.total) * 100
    }

    return result
  }
}

// QR Code utility functions
export const qrCodeUtils = {
  // Validate QR code and extract student information
  validateQRCode: async (qrData: string): Promise<QRValidationResultInput> => {
    try {
      // Find the QR code in database
      const qrCode = await prisma.qRCode.findUnique({
        where: { qrData },
        include: {
          student: {
            select: {
              id: true,
              studentNumber: true,
              firstName: true,
              lastName: true,
              gradeLevel: true,
              section: true,
              status: true
            }
          }
        }
      })

      if (!qrCode) {
        return {
          valid: false,
          reason: 'QR code not found'
        }
      }

      if (!qrCode.isActive) {
        return {
          valid: false,
          reason: 'QR code is inactive'
        }
      }

      if (qrCode.expiresAt && qrCode.expiresAt < new Date()) {
        return {
          valid: false,
          reason: 'QR code has expired'
        }
      }

      if (qrCode.student.status !== StudentStatus.ACTIVE) {
        return {
          valid: false,
          reason: 'Student is not active'
        }
      }

      return {
        valid: true,
        studentId: qrCode.studentId,
        student: qrCode.student
      }
    } catch (error) {
      console.error('QR validation error:', error)
      return {
        valid: false,
        reason: 'QR code validation failed'
      }
    }
  },

  // Check if QR code was already scanned today
  checkDuplicateScan: async (studentId: string, date: Date = new Date()) => {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(date)
    endOfDay.setHours(23, 59, 59, 999)

    const existing = await prisma.attendance.findFirst({
      where: {
        studentId,
        date: {
          gte: startOfDay,
          lte: endOfDay
        }
      }
    })

    return existing !== null
  },

  // Generate new QR code for student
  generateQRCode: async (studentId: string, expiryHours: number = 24) => {
    const qrData = `QRSAMS_${studentId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const expiresAt = new Date(Date.now() + (expiryHours * 60 * 60 * 1000))

    // Deactivate existing QR codes for this student
    await prisma.qRCode.updateMany({
      where: { studentId },
      data: { isActive: false }
    })

    // Create new QR code
    const newQRCode = await prisma.qRCode.create({
      data: {
        studentId,
        qrData,
        expiresAt,
        isActive: true
      }
    })

    return newQRCode
  }
}

// Attendance status calculation utilities
export const attendanceStatusUtils = {
  // Calculate attendance status based on time and rules
  calculateStatus: (timeIn: Date, cutoffTime: string = '08:00'): AttendanceStatus => {
    const [hours, minutes] = cutoffTime.split(':').map(Number)
    const cutoff = new Date(timeIn)
    cutoff.setHours(hours, minutes, 0, 0)

    if (timeIn <= cutoff) {
      return AttendanceStatus.PRESENT
    } else {
      return AttendanceStatus.LATE
    }
  },

  // Get attendance status display text
  getStatusDisplay: (status: AttendanceStatus): string => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'Present'
      case AttendanceStatus.ABSENT:
        return 'Absent'
      case AttendanceStatus.LATE:
        return 'Late'
      case AttendanceStatus.EXCUSED:
        return 'Excused'
      default:
        return 'Unknown'
    }
  },

  // Get status color for UI
  getStatusColor: (status: AttendanceStatus): string => {
    switch (status) {
      case AttendanceStatus.PRESENT:
        return 'green'
      case AttendanceStatus.ABSENT:
        return 'red'
      case AttendanceStatus.LATE:
        return 'yellow'
      case AttendanceStatus.EXCUSED:
        return 'blue'
      default:
        return 'gray'
    }
  }
}

// Analytics utilities for attendance data
export const attendanceAnalyticsUtils = {
  // Get daily attendance trends
  getDailyTrends: async (startDate: Date, endDate: Date, filters?: {
    gradeLevel?: string
    section?: string
  }) => {
    const where: any = {
      date: {
        gte: startDate,
        lte: endDate
      }
    }

    if (filters?.gradeLevel || filters?.section) {
      where.student = {}
      if (filters.gradeLevel) {
        where.student.gradeLevel = filters.gradeLevel
      }
      if (filters.section) {
        where.student.section = filters.section
      }
    }

    const dailyData = await prisma.attendance.groupBy({
      by: ['date', 'status'],
      where,
      _count: {
        status: true
      },
      orderBy: {
        date: 'asc'
      }
    })

    // Process data into daily trends
    const trendsMap = new Map()

    dailyData.forEach(item => {
      const dateKey = item.date.toISOString().split('T')[0]
      if (!trendsMap.has(dateKey)) {
        trendsMap.set(dateKey, {
          date: dateKey,
          present: 0,
          absent: 0,
          late: 0,
          excused: 0,
          total: 0
        })
      }

      const dayData = trendsMap.get(dateKey)
      dayData.total += item._count.status

      switch (item.status) {
        case AttendanceStatus.PRESENT:
          dayData.present = item._count.status
          break
        case AttendanceStatus.ABSENT:
          dayData.absent = item._count.status
          break
        case AttendanceStatus.LATE:
          dayData.late = item._count.status
          break
        case AttendanceStatus.EXCUSED:
          dayData.excused = item._count.status
          break
      }
    })

    return Array.from(trendsMap.values()).map(day => ({
      ...day,
      attendanceRate: day.total > 0 ? ((day.present + day.late + day.excused) / day.total) * 100 : 0
    }))
  },

  // Get attendance by grade/section
  getAttendanceByGradeSection: async (startDate: Date, endDate: Date) => {
    const data = await prisma.attendance.groupBy({
      by: ['status'],
      where: {
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: {
        status: true
      }
    })

    // Get grade/section breakdown
    const gradeData = await prisma.$queryRaw`
      SELECT
        s.gradeLevel,
        s.section,
        a.status,
        COUNT(*) as count
      FROM attendances a
      JOIN students s ON a.studentId = s.id
      WHERE a.date >= ${startDate} AND a.date <= ${endDate}
      GROUP BY s.gradeLevel, s.section, a.status
      ORDER BY s.gradeLevel, s.section
    ` as any[]

    // Process grade/section data
    const gradeMap = new Map()

    gradeData.forEach(item => {
      const key = `${item.gradeLevel}-${item.section}`
      if (!gradeMap.has(key)) {
        gradeMap.set(key, {
          gradeLevel: item.gradeLevel,
          section: item.section,
          present: 0,
          absent: 0,
          late: 0,
          excused: 0,
          total: 0
        })
      }

      const sectionData = gradeMap.get(key)
      sectionData.total += Number(item.count)

      switch (item.status) {
        case AttendanceStatus.PRESENT:
          sectionData.present = Number(item.count)
          break
        case AttendanceStatus.ABSENT:
          sectionData.absent = Number(item.count)
          break
        case AttendanceStatus.LATE:
          sectionData.late = Number(item.count)
          break
        case AttendanceStatus.EXCUSED:
          sectionData.excused = Number(item.count)
          break
      }
    })

    return Array.from(gradeMap.values()).map(section => ({
      ...section,
      attendanceRate: section.total > 0 ? ((section.present + section.late + section.excused) / section.total) * 100 : 0
    }))
  },

  // Get at-risk students (low attendance)
  getAtRiskStudents: async (startDate: Date, endDate: Date, threshold: number = 80) => {
    const studentAttendance = await prisma.$queryRaw`
      SELECT
        s.id,
        s.studentNumber,
        s.firstName,
        s.lastName,
        s.gradeLevel,
        s.section,
        COUNT(*) as totalDays,
        SUM(CASE WHEN a.status IN ('PRESENT', 'LATE', 'EXCUSED') THEN 1 ELSE 0 END) as attendedDays,
        SUM(CASE WHEN a.status = 'ABSENT' THEN 1 ELSE 0 END) as absentDays,
        (SUM(CASE WHEN a.status IN ('PRESENT', 'LATE', 'EXCUSED') THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as attendanceRate
      FROM students s
      LEFT JOIN attendances a ON s.id = a.studentId
        AND a.date >= ${startDate} AND a.date <= ${endDate}
      WHERE s.status = 'ACTIVE'
      GROUP BY s.id, s.studentNumber, s.firstName, s.lastName, s.gradeLevel, s.section
      HAVING attendanceRate < ${threshold}
      ORDER BY attendanceRate ASC
    ` as any[]

    return studentAttendance.map(student => ({
      id: student.id,
      studentNumber: student.studentNumber,
      name: `${student.firstName} ${student.lastName}`,
      gradeLevel: student.gradeLevel,
      section: student.section,
      totalDays: Number(student.totalDays),
      attendedDays: Number(student.attendedDays),
      absentDays: Number(student.absentDays),
      attendanceRate: Number(student.attendanceRate),
      riskLevel: Number(student.attendanceRate) < 60 ? 'high' :
                 Number(student.attendanceRate) < 75 ? 'medium' : 'low'
    }))
  }
}

// Transform utilities for API responses
export const transformUtils = {
  // Transform attendance records for API response
  transformAttendanceForResponse: (attendance: any) => {
    return {
      id: attendance.id,
      studentId: attendance.studentId,
      teacherId: attendance.teacherId,
      date: attendance.date,
      timeIn: attendance.timeIn,
      timeOut: attendance.timeOut,
      status: attendance.status,
      remarks: attendance.remarks,
      createdAt: attendance.createdAt,
      updatedAt: attendance.updatedAt,
      student: attendance.student ? {
        id: attendance.student.id,
        studentNumber: attendance.student.studentNumber,
        firstName: attendance.student.firstName,
        lastName: attendance.student.lastName,
        middleName: attendance.student.middleName,
        fullName: `${attendance.student.firstName} ${attendance.student.lastName}`,
        gradeLevel: attendance.student.gradeLevel,
        section: attendance.student.section,
        status: attendance.student.status
      } : undefined,
      teacher: attendance.teacher ? {
        id: attendance.teacher.id,
        employeeNumber: attendance.teacher.employeeNumber,
        firstName: attendance.teacher.firstName,
        lastName: attendance.teacher.lastName,
        middleName: attendance.teacher.middleName,
        fullName: `${attendance.teacher.firstName} ${attendance.teacher.lastName}`
      } : undefined,
      statusDisplay: attendanceStatusUtils.getStatusDisplay(attendance.status),
      statusColor: attendanceStatusUtils.getStatusColor(attendance.status)
    }
  },

  // Calculate pagination metadata
  calculatePagination: (page: number, limit: number, total: number) => {
    const totalPages = Math.ceil(total / limit)
    const hasNext = page < totalPages
    const hasPrev = page > 1

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : null,
      prevPage: hasPrev ? page - 1 : null
    }
  }
}
