import { z } from 'zod'
import { AttendanceStatus } from '../../generated/prisma'

// QR Code scan validation schema
export const qrScanSchema = z.object({
  qrData: z.string()
    .min(1, 'QR code data is required')
    .max(500, 'QR code data too long'),
  
  location: z.string()
    .max(100, 'Location must be less than 100 characters')
    .optional(),
  
  notes: z.string()
    .max(500, 'Notes must be less than 500 characters')
    .optional(),
  
  timestamp: z.coerce.date().optional() // For offline scans
})

// Manual attendance entry schema
export const manualAttendanceSchema = z.object({
  studentId: z.string()
    .cuid('Invalid student ID format'),
  
  status: z.nativeEnum(AttendanceStatus),
  
  timeIn: z.coerce.date().optional(),
  
  timeOut: z.coerce.date().optional(),
  
  remarks: z.string()
    .max(500, 'Remarks must be less than 500 characters')
    .optional(),
  
  date: z.coerce.date().optional() // Defaults to today if not provided
})

// Attendance update schema
export const updateAttendanceSchema = z.object({
  status: z.nativeEnum(AttendanceStatus).optional(),
  
  timeIn: z.coerce.date().optional(),
  
  timeOut: z.coerce.date().optional(),
  
  remarks: z.string()
    .max(500, 'Remarks must be less than 500 characters')
    .optional()
})

// Attendance query/filter schema
export const attendanceQuerySchema = z.object({
  // Pagination
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  
  // Date filters
  date: z.coerce.date().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  
  // Student filters
  studentId: z.string().cuid().optional(),
  gradeLevel: z.string().optional(),
  section: z.string().optional(),
  
  // Status filters
  status: z.nativeEnum(AttendanceStatus).optional(),
  
  // Teacher filter
  teacherId: z.string().cuid().optional(),
  
  // Search
  search: z.string().optional(), // Search by student name or number
  
  // Sorting
  sortBy: z.enum([
    'date', 'timeIn', 'timeOut', 'status', 'studentName', 'createdAt'
  ]).default('date'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  
  // Include related data
  includeStudent: z.coerce.boolean().default(true),
  includeTeacher: z.coerce.boolean().default(false),
  includeSMSLogs: z.coerce.boolean().default(false)
})

// Today's attendance summary schema
export const todayAttendanceSchema = z.object({
  gradeLevel: z.string().optional(),
  section: z.string().optional(),
  includeDetails: z.coerce.boolean().default(false)
})

// Student attendance history schema
export const studentAttendanceHistorySchema = z.object({
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  status: z.nativeEnum(AttendanceStatus).optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20)
})

// Class attendance report schema
export const classAttendanceReportSchema = z.object({
  classId: z.string().cuid(),
  date: z.coerce.date().optional(),
  startDate: z.coerce.date().optional(),
  endDate: z.coerce.date().optional(),
  includeAbsent: z.coerce.boolean().default(true),
  format: z.enum(['json', 'csv', 'pdf']).default('json')
})

// Bulk attendance update schema
export const bulkAttendanceUpdateSchema = z.object({
  updates: z.array(z.object({
    studentId: z.string().cuid(),
    status: z.nativeEnum(AttendanceStatus),
    timeIn: z.coerce.date().optional(),
    timeOut: z.coerce.date().optional(),
    remarks: z.string().max(500).optional(),
    date: z.coerce.date().optional()
  })).min(1, 'At least one update is required').max(100, 'Cannot update more than 100 records at once'),
  
  teacherId: z.string().cuid(),
  
  overwriteExisting: z.boolean().default(false)
})

// Attendance analytics filters schema
export const attendanceAnalyticsSchema = z.object({
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
  
  gradeLevel: z.string().optional(),
  section: z.string().optional(),
  
  groupBy: z.enum(['day', 'week', 'month', 'grade', 'section']).default('day'),
  
  includeWeekends: z.coerce.boolean().default(false),
  
  metrics: z.array(z.enum([
    'attendance_rate', 'present_count', 'absent_count', 'late_count', 
    'excused_count', 'total_students', 'trends'
  ])).default(['attendance_rate', 'present_count', 'absent_count'])
})

// Attendance export schema
export const attendanceExportSchema = z.object({
  format: z.enum(['csv', 'excel', 'pdf']).default('csv'),
  
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
  
  gradeLevel: z.string().optional(),
  section: z.string().optional(),
  status: z.nativeEnum(AttendanceStatus).optional(),
  
  includeStudentDetails: z.coerce.boolean().default(true),
  includeStatistics: z.coerce.boolean().default(false),
  
  fields: z.array(z.enum([
    'date', 'studentNumber', 'studentName', 'gradeLevel', 'section',
    'status', 'timeIn', 'timeOut', 'remarks', 'teacherName'
  ])).optional()
})

// Offline queue item schema
export const offlineQueueItemSchema = z.object({
  studentId: z.string().cuid(),
  action: z.nativeEnum(AttendanceStatus),
  timestamp: z.coerce.date(),
  location: z.string().max(100).optional(),
  notes: z.string().max(500).optional(),
  queueId: z.string().optional() // For tracking offline items
})

// Batch offline sync schema
export const offlineSyncSchema = z.object({
  items: z.array(offlineQueueItemSchema)
    .min(1, 'At least one item is required')
    .max(500, 'Cannot sync more than 500 items at once'),
  
  deviceId: z.string().optional(),
  syncTimestamp: z.coerce.date().optional()
})

// Attendance ID parameter schema
export const attendanceIdSchema = z.object({
  id: z.string().cuid('Invalid attendance ID format')
})

// Student ID parameter schema
export const studentIdParamSchema = z.object({
  id: z.string().cuid('Invalid student ID format')
})

// Class ID parameter schema
export const classIdParamSchema = z.object({
  classId: z.string().cuid('Invalid class ID format')
})

// QR code validation result schema
export const qrValidationResultSchema = z.object({
  valid: z.boolean(),
  studentId: z.string().cuid().optional(),
  reason: z.string().optional(),
  student: z.object({
    id: z.string(),
    studentNumber: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    gradeLevel: z.string(),
    section: z.string()
  }).optional()
})

// Type exports
export type QRScanInput = z.infer<typeof qrScanSchema>
export type ManualAttendanceInput = z.infer<typeof manualAttendanceSchema>
export type UpdateAttendanceInput = z.infer<typeof updateAttendanceSchema>
export type AttendanceQueryInput = z.infer<typeof attendanceQuerySchema>
export type TodayAttendanceInput = z.infer<typeof todayAttendanceSchema>
export type StudentAttendanceHistoryInput = z.infer<typeof studentAttendanceHistorySchema>
export type ClassAttendanceReportInput = z.infer<typeof classAttendanceReportSchema>
export type BulkAttendanceUpdateInput = z.infer<typeof bulkAttendanceUpdateSchema>
export type AttendanceAnalyticsInput = z.infer<typeof attendanceAnalyticsSchema>
export type AttendanceExportInput = z.infer<typeof attendanceExportSchema>
export type OfflineQueueItemInput = z.infer<typeof offlineQueueItemSchema>
export type OfflineSyncInput = z.infer<typeof offlineSyncSchema>
export type AttendanceIdInput = z.infer<typeof attendanceIdSchema>
export type StudentIdParamInput = z.infer<typeof studentIdParamSchema>
export type ClassIdParamInput = z.infer<typeof classIdParamSchema>
export type QRValidationResultInput = z.infer<typeof qrValidationResultSchema>

// Validation helper functions
export const validateTimeRange = (timeIn?: Date, timeOut?: Date) => {
  if (timeIn && timeOut && timeOut <= timeIn) {
    return { valid: false, error: 'Time out must be after time in' }
  }
  return { valid: true }
}

export const validateAttendanceDate = (date: Date) => {
  const today = new Date()
  const maxPastDays = 30 // Allow attendance entry up to 30 days in the past
  const minDate = new Date(today.getTime() - (maxPastDays * 24 * 60 * 60 * 1000))
  
  if (date < minDate) {
    return { valid: false, error: `Cannot record attendance more than ${maxPastDays} days in the past` }
  }
  
  if (date > today) {
    return { valid: false, error: 'Cannot record attendance for future dates' }
  }
  
  return { valid: true }
}

// Error messages
export const attendanceValidationErrors = {
  QR_CODE_INVALID: 'Invalid or expired QR code',
  QR_CODE_ALREADY_SCANNED: 'QR code already scanned today',
  STUDENT_NOT_FOUND: 'Student not found',
  ATTENDANCE_EXISTS: 'Attendance already recorded for this date',
  INVALID_TIME_RANGE: 'Time out must be after time in',
  INVALID_DATE: 'Invalid attendance date',
  BULK_LIMIT_EXCEEDED: 'Bulk operation limit exceeded',
  OFFLINE_SYNC_FAILED: 'Failed to sync offline data'
} as const
