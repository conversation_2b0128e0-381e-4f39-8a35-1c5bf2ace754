import { prisma } from '@/lib/db'
import { AttendanceStatus } from '../../generated/prisma'
import { attendanceDbUtils, qrCodeUtils, attendanceStatusUtils } from './attendance'
import { smsUtils } from './sms'
import type { OfflineQueueItemInput } from '@/lib/validations/attendance'

// Offline queue processing utilities
export const offlineQueueUtils = {
  // Process a single offline queue item
  processQueueItem: async (item: OfflineQueueItemInput, teacherId: string) => {
    try {
      // Check if attendance already exists for this student and date
      const existingAttendance = await attendanceDbUtils.checkAttendanceExists(
        item.studentId,
        item.timestamp
      )

      if (existingAttendance) {
        return {
          success: false,
          error: 'Attendance already recorded for this date',
          attendanceId: existingAttendance.id
        }
      }

      // Validate student exists and is active
      const student = await prisma.student.findUnique({
        where: { id: item.studentId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          status: true
        }
      })

      if (!student) {
        return {
          success: false,
          error: 'Student not found'
        }
      }

      if (student.status !== 'ACTIVE') {
        return {
          success: false,
          error: 'Student is not active'
        }
      }

      // Create attendance record
      const attendance = await prisma.attendance.create({
        data: {
          studentId: item.studentId,
          teacherId,
          date: item.timestamp,
          timeIn: item.action === AttendanceStatus.PRESENT || item.action === AttendanceStatus.LATE 
            ? item.timestamp : undefined,
          status: item.action,
          remarks: item.notes || `Offline sync - ${item.location || 'Unknown location'}`
        }
      })

      // Send SMS notification if enabled
      try {
        await smsUtils.sendAttendanceNotification(
          item.studentId,
          attendance.id,
          item.action,
          attendance.timeIn
        )
      } catch (smsError) {
        console.warn('SMS notification failed for offline sync:', smsError)
        // Don't fail the entire operation if SMS fails
      }

      return {
        success: true,
        attendanceId: attendance.id,
        message: 'Attendance recorded successfully'
      }
    } catch (error) {
      console.error('Queue item processing error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Processing failed'
      }
    }
  },

  // Process multiple offline queue items
  processBatchQueue: async (items: OfflineQueueItemInput[], teacherId: string) => {
    const results = []
    let successCount = 0
    let failureCount = 0

    for (const item of items) {
      const result = await offlineQueueUtils.processQueueItem(item, teacherId)
      
      results.push({
        queueId: item.queueId,
        studentId: item.studentId,
        timestamp: item.timestamp,
        ...result
      })

      if (result.success) {
        successCount++
      } else {
        failureCount++
      }
    }

    return {
      total: items.length,
      success: successCount,
      failed: failureCount,
      results
    }
  },

  // Validate offline queue items before processing
  validateQueueItems: async (items: OfflineQueueItemInput[]) => {
    const validationResults = []

    for (const item of items) {
      const validation = {
        queueId: item.queueId,
        studentId: item.studentId,
        valid: true,
        errors: [] as string[]
      }

      // Check if student exists
      const student = await prisma.student.findUnique({
        where: { id: item.studentId },
        select: { id: true, status: true }
      })

      if (!student) {
        validation.valid = false
        validation.errors.push('Student not found')
      } else if (student.status !== 'ACTIVE') {
        validation.valid = false
        validation.errors.push('Student is not active')
      }

      // Check for duplicate attendance
      const existingAttendance = await attendanceDbUtils.checkAttendanceExists(
        item.studentId,
        item.timestamp
      )

      if (existingAttendance) {
        validation.valid = false
        validation.errors.push('Attendance already recorded for this date')
      }

      // Validate timestamp (not too old, not in future)
      const now = new Date()
      const maxPastDays = 7 // Allow up to 7 days old for offline sync
      const minDate = new Date(now.getTime() - (maxPastDays * 24 * 60 * 60 * 1000))

      if (item.timestamp < minDate) {
        validation.valid = false
        validation.errors.push(`Timestamp too old (max ${maxPastDays} days)`)
      }

      if (item.timestamp > now) {
        validation.valid = false
        validation.errors.push('Timestamp cannot be in the future')
      }

      validationResults.push(validation)
    }

    return validationResults
  },

  // Clean up old offline queue items (for client-side cleanup)
  getProcessedItems: async (queueIds: string[]) => {
    // This would typically be used by the client to check which items were processed
    // and can be removed from local storage
    const processedItems = []

    for (const queueId of queueIds) {
      // Check if there's an attendance record that matches this queue item
      // This is a simplified check - in practice, you might want to store
      // queue IDs in the attendance records for better tracking
      const exists = await prisma.attendance.findFirst({
        where: {
          remarks: {
            contains: queueId
          }
        }
      })

      if (exists) {
        processedItems.push(queueId)
      }
    }

    return processedItems
  }
}

// Offline sync statistics utilities
export const offlineSyncStatsUtils = {
  // Get offline sync statistics
  getSyncStats: async (startDate: Date, endDate: Date) => {
    // Get attendance records that were created from offline sync
    const offlineAttendance = await prisma.attendance.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        remarks: {
          contains: 'Offline sync'
        }
      },
      select: {
        id: true,
        status: true,
        createdAt: true,
        date: true
      }
    })

    const stats = {
      totalOfflineRecords: offlineAttendance.length,
      byStatus: {
        present: 0,
        absent: 0,
        late: 0,
        excused: 0
      },
      byDay: new Map<string, number>(),
      averageDelayHours: 0
    }

    let totalDelayHours = 0

    offlineAttendance.forEach(record => {
      // Count by status
      switch (record.status) {
        case AttendanceStatus.PRESENT:
          stats.byStatus.present++
          break
        case AttendanceStatus.ABSENT:
          stats.byStatus.absent++
          break
        case AttendanceStatus.LATE:
          stats.byStatus.late++
          break
        case AttendanceStatus.EXCUSED:
          stats.byStatus.excused++
          break
      }

      // Count by day
      const dayKey = record.createdAt.toISOString().split('T')[0]
      stats.byDay.set(dayKey, (stats.byDay.get(dayKey) || 0) + 1)

      // Calculate delay (difference between attendance date and creation date)
      const delayMs = record.createdAt.getTime() - record.date.getTime()
      const delayHours = delayMs / (1000 * 60 * 60)
      totalDelayHours += delayHours
    })

    if (offlineAttendance.length > 0) {
      stats.averageDelayHours = totalDelayHours / offlineAttendance.length
    }

    return {
      ...stats,
      byDay: Object.fromEntries(stats.byDay)
    }
  }
}

// Queue conflict resolution utilities
export const queueConflictUtils = {
  // Resolve conflicts when multiple offline entries exist for same student/date
  resolveConflicts: async (items: OfflineQueueItemInput[]) => {
    // Group items by student and date
    const groupedItems = new Map<string, OfflineQueueItemInput[]>()

    items.forEach(item => {
      const key = `${item.studentId}_${item.timestamp.toISOString().split('T')[0]}`
      if (!groupedItems.has(key)) {
        groupedItems.set(key, [])
      }
      groupedItems.get(key)!.push(item)
    })

    const resolvedItems: OfflineQueueItemInput[] = []

    // Resolve conflicts for each group
    groupedItems.forEach((groupItems, key) => {
      if (groupItems.length === 1) {
        // No conflict
        resolvedItems.push(groupItems[0])
      } else {
        // Resolve conflict using priority rules
        const resolved = queueConflictUtils.applyConflictResolution(groupItems)
        resolvedItems.push(resolved)
      }
    })

    return resolvedItems
  },

  // Apply conflict resolution rules
  applyConflictResolution: (conflictingItems: OfflineQueueItemInput[]): OfflineQueueItemInput => {
    // Priority rules (highest to lowest):
    // 1. PRESENT/LATE over ABSENT
    // 2. Later timestamp (more recent scan)
    // 3. Items with notes/location info

    const priorityOrder = {
      [AttendanceStatus.PRESENT]: 4,
      [AttendanceStatus.LATE]: 3,
      [AttendanceStatus.EXCUSED]: 2,
      [AttendanceStatus.ABSENT]: 1
    }

    // Sort by priority rules
    const sorted = conflictingItems.sort((a, b) => {
      // First by status priority
      const statusDiff = priorityOrder[b.action] - priorityOrder[a.action]
      if (statusDiff !== 0) return statusDiff

      // Then by timestamp (more recent first)
      const timeDiff = b.timestamp.getTime() - a.timestamp.getTime()
      if (timeDiff !== 0) return timeDiff

      // Finally by presence of additional info
      const aHasInfo = (a.notes?.length || 0) + (a.location?.length || 0)
      const bHasInfo = (b.notes?.length || 0) + (b.location?.length || 0)
      return bHasInfo - aHasInfo
    })

    // Return the highest priority item with merged information
    const winner = sorted[0]
    const mergedNotes = conflictingItems
      .map(item => item.notes)
      .filter(Boolean)
      .join('; ')

    return {
      ...winner,
      notes: mergedNotes || winner.notes
    }
  }
}
