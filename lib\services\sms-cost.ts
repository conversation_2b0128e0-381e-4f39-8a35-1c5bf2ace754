import { prisma } from '@/lib/db'
import { 
  SMSStats, 
  SMSCostSummary, 
  SMSUsageReport 
} from '@/lib/types/sms'
import { SMSStatus, SMSType } from '../../generated/prisma'

export class SMSCostService {
  
  /**
   * Get SMS statistics for a date range
   */
  static async getSMSStats(startDate: Date, endDate: Date): Promise<SMSStats> {
    const stats = await prisma.sMSLog.groupBy({
      by: ['status'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: { status: true },
      _sum: { cost: true }
    })

    const result: SMSStats = {
      total: 0,
      pending: 0,
      queued: 0,
      sent: 0,
      delivered: 0,
      failed: 0,
      cancelled: 0,
      deliveryRate: 0,
      successRate: 0,
      totalCost: 0,
      avgCostPerSMS: 0
    }

    let totalCost = 0

    stats.forEach(stat => {
      result.total += stat._count.status
      totalCost += stat._sum.cost || 0

      switch (stat.status) {
        case SMSStatus.PENDING:
          result.pending = stat._count.status
          break
        case SMSStatus.QUEUED:
          result.queued = stat._count.status
          break
        case SMSStatus.SENT:
          result.sent = stat._count.status
          break
        case SMSStatus.DELIVERED:
          result.delivered = stat._count.status
          break
        case SMSStatus.FAILED:
          result.failed = stat._count.status
          break
        case SMSStatus.CANCELLED:
          result.cancelled = stat._count.status
          break
      }
    })

    result.totalCost = totalCost
    result.avgCostPerSMS = result.total > 0 ? totalCost / result.total : 0
    result.deliveryRate = result.sent > 0 ? (result.delivered / result.sent) * 100 : 0
    result.successRate = result.total > 0 ? ((result.sent + result.delivered) / result.total) * 100 : 0

    return result
  }

  /**
   * Get daily cost summaries for a date range
   */
  static async getDailyCosts(startDate: Date, endDate: Date): Promise<SMSCostSummary[]> {
    const costs = await prisma.sMSCost.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { date: 'asc' }
    })

    return costs.map(cost => ({
      date: cost.date,
      totalSent: cost.totalSent,
      totalCost: cost.totalCost,
      avgCostPerSMS: cost.avgCostPerSMS
    }))
  }

  /**
   * Get comprehensive usage report
   */
  static async getUsageReport(startDate: Date, endDate: Date): Promise<SMSUsageReport> {
    // Get overall stats
    const stats = await this.getSMSStats(startDate, endDate)
    
    // Get daily costs
    const dailyCosts = await this.getDailyCosts(startDate, endDate)

    // Get top recipients
    const topRecipients = await prisma.sMSLog.groupBy({
      by: ['recipientNumber'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        status: {
          in: [SMSStatus.SENT, SMSStatus.DELIVERED]
        }
      },
      _count: { recipientNumber: true },
      _sum: { cost: true },
      orderBy: { _count: { recipientNumber: 'desc' } },
      take: 10
    })

    // Get message types breakdown
    const messageTypes = await prisma.sMSLog.groupBy({
      by: ['type'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: { type: true },
      _sum: { cost: true }
    })

    return {
      period: { start: startDate, end: endDate },
      stats,
      dailyCosts,
      topRecipients: topRecipients.map(recipient => ({
        recipientNumber: recipient.recipientNumber,
        count: recipient._count.recipientNumber,
        cost: recipient._sum.cost || 0
      })),
      messageTypes: messageTypes.map(type => ({
        type: type.type,
        count: type._count.type,
        cost: type._sum.cost || 0
      }))
    }
  }

  /**
   * Get current month's spending
   */
  static async getCurrentMonthSpending(): Promise<{
    totalCost: number
    totalMessages: number
    avgCostPerMessage: number
    dailyAverage: number
    projectedMonthly: number
  }> {
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0)

    const stats = await this.getSMSStats(startOfMonth, endOfMonth)
    const daysInMonth = endOfMonth.getDate()
    const daysPassed = now.getDate()

    const dailyAverage = daysPassed > 0 ? stats.totalCost / daysPassed : 0
    const projectedMonthly = dailyAverage * daysInMonth

    return {
      totalCost: stats.totalCost,
      totalMessages: stats.total,
      avgCostPerMessage: stats.avgCostPerSMS,
      dailyAverage,
      projectedMonthly
    }
  }

  /**
   * Check if daily budget limit is exceeded
   */
  static async checkDailyBudgetLimit(date?: Date): Promise<{
    isExceeded: boolean
    currentSpending: number
    budgetLimit: number
    remainingBudget: number
    percentageUsed: number
  }> {
    const targetDate = date || new Date()
    targetDate.setHours(0, 0, 0, 0)

    // Get daily budget limit from settings
    const budgetSetting = await prisma.sMSSetting.findUnique({
      where: { key: 'daily_budget_limit' }
    })
    
    const budgetLimit = budgetSetting ? parseFloat(budgetSetting.value) : 500 // Default 500 PHP

    // Get current day's spending
    const dailyCost = await prisma.sMSCost.findUnique({
      where: { date: targetDate }
    })

    const currentSpending = dailyCost?.totalCost || 0
    const remainingBudget = Math.max(0, budgetLimit - currentSpending)
    const percentageUsed = budgetLimit > 0 ? (currentSpending / budgetLimit) * 100 : 0

    return {
      isExceeded: currentSpending > budgetLimit,
      currentSpending,
      budgetLimit,
      remainingBudget,
      percentageUsed
    }
  }

  /**
   * Get budget alerts
   */
  static async getBudgetAlerts(): Promise<Array<{
    type: 'daily' | 'monthly' | 'weekly'
    severity: 'warning' | 'critical'
    message: string
    currentAmount: number
    limitAmount: number
    percentageUsed: number
  }>> {
    const alerts = []

    // Check daily budget
    const dailyBudget = await this.checkDailyBudgetLimit()
    if (dailyBudget.percentageUsed >= 80) {
      alerts.push({
        type: 'daily' as const,
        severity: dailyBudget.percentageUsed >= 100 ? 'critical' as const : 'warning' as const,
        message: dailyBudget.isExceeded 
          ? `Daily budget exceeded by ₱${(dailyBudget.currentSpending - dailyBudget.budgetLimit).toFixed(2)}`
          : `Daily budget ${dailyBudget.percentageUsed.toFixed(1)}% used`,
        currentAmount: dailyBudget.currentSpending,
        limitAmount: dailyBudget.budgetLimit,
        percentageUsed: dailyBudget.percentageUsed
      })
    }

    // Check monthly projection
    const monthlySpending = await this.getCurrentMonthSpending()
    const monthlyBudgetSetting = await prisma.sMSSetting.findUnique({
      where: { key: 'monthly_budget_limit' }
    })
    const monthlyBudgetLimit = monthlyBudgetSetting ? parseFloat(monthlyBudgetSetting.value) : 15000 // Default 15,000 PHP

    const monthlyPercentage = (monthlySpending.projectedMonthly / monthlyBudgetLimit) * 100

    if (monthlyPercentage >= 80) {
      alerts.push({
        type: 'monthly' as const,
        severity: monthlyPercentage >= 100 ? 'critical' as const : 'warning' as const,
        message: monthlyPercentage >= 100
          ? `Projected monthly spending exceeds budget by ₱${(monthlySpending.projectedMonthly - monthlyBudgetLimit).toFixed(2)}`
          : `Projected monthly spending at ${monthlyPercentage.toFixed(1)}% of budget`,
        currentAmount: monthlySpending.projectedMonthly,
        limitAmount: monthlyBudgetLimit,
        percentageUsed: monthlyPercentage
      })
    }

    return alerts
  }

  /**
   * Get cost breakdown by category
   */
  static async getCostBreakdown(startDate: Date, endDate: Date): Promise<Array<{
    category: string
    count: number
    cost: number
    percentage: number
  }>> {
    const breakdown = await prisma.sMSLog.groupBy({
      by: ['type'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        },
        cost: { not: null }
      },
      _count: { type: true },
      _sum: { cost: true }
    })

    const totalCost = breakdown.reduce((sum, item) => sum + (item._sum.cost || 0), 0)

    return breakdown.map(item => ({
      category: item.type,
      count: item._count.type,
      cost: item._sum.cost || 0,
      percentage: totalCost > 0 ? ((item._sum.cost || 0) / totalCost) * 100 : 0
    }))
  }

  /**
   * Get failed messages for cost analysis
   */
  static async getFailedMessagesCost(startDate: Date, endDate: Date): Promise<{
    totalFailed: number
    potentialCostSaved: number
    failureReasons: Array<{
      reason: string
      count: number
      potentialCost: number
    }>
  }> {
    const failedMessages = await prisma.sMSLog.findMany({
      where: {
        status: SMSStatus.FAILED,
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      select: {
        errorMessage: true,
        message: true
      }
    })

    const avgCostPerSMS = parseFloat(process.env.SMS_COST_PER_MESSAGE || '2.50')
    const totalFailed = failedMessages.length
    const potentialCostSaved = totalFailed * avgCostPerSMS

    // Group by failure reasons
    const reasonMap = new Map<string, number>()
    failedMessages.forEach(msg => {
      const reason = msg.errorMessage || 'Unknown error'
      reasonMap.set(reason, (reasonMap.get(reason) || 0) + 1)
    })

    const failureReasons = Array.from(reasonMap.entries()).map(([reason, count]) => ({
      reason,
      count,
      potentialCost: count * avgCostPerSMS
    }))

    return {
      totalFailed,
      potentialCostSaved,
      failureReasons
    }
  }

  /**
   * Update SMS settings
   */
  static async updateSMSSetting(
    key: string, 
    value: string, 
    description?: string,
    updatedBy?: string
  ): Promise<void> {
    await prisma.sMSSetting.upsert({
      where: { key },
      update: { 
        value, 
        description,
        updatedBy,
        updatedAt: new Date()
      },
      create: { 
        key, 
        value, 
        description,
        updatedBy
      }
    })
  }

  /**
   * Get SMS setting
   */
  static async getSMSSetting(key: string): Promise<string | null> {
    const setting = await prisma.sMSSetting.findUnique({
      where: { key }
    })
    return setting?.value || null
  }

  /**
   * Get all SMS settings
   */
  static async getAllSMSSettings(): Promise<Record<string, string>> {
    const settings = await prisma.sMSSetting.findMany()
    const result: Record<string, string> = {}
    
    settings.forEach(setting => {
      result[setting.key] = setting.value
    })
    
    return result
  }

  /**
   * Initialize default SMS settings
   */
  static async initializeDefaultSettings(): Promise<void> {
    const defaultSettings = [
      { key: 'daily_budget_limit', value: '500', description: 'Daily SMS budget limit in PHP' },
      { key: 'monthly_budget_limit', value: '15000', description: 'Monthly SMS budget limit in PHP' },
      { key: 'cost_per_sms', value: '2.50', description: 'Cost per SMS in PHP' },
      { key: 'max_retries', value: '3', description: 'Maximum retry attempts for failed SMS' },
      { key: 'retry_delay', value: '60000', description: 'Retry delay in milliseconds' },
      { key: 'rate_limit_per_minute', value: '60', description: 'Maximum SMS per minute' },
      { key: 'enable_cost_tracking', value: 'true', description: 'Enable SMS cost tracking' },
      { key: 'enable_budget_alerts', value: 'true', description: 'Enable budget alert notifications' },
      { key: 'budget_alert_threshold', value: '80', description: 'Budget alert threshold percentage' }
    ]

    for (const setting of defaultSettings) {
      const existing = await prisma.sMSSetting.findUnique({
        where: { key: setting.key }
      })

      if (!existing) {
        await prisma.sMSSetting.create({
          data: setting
        })
      }
    }
  }
}
