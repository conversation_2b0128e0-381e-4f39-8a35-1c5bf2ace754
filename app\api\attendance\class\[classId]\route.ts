import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  classAttendanceReportSchema,
  classIdParamSchema,
  type ClassAttendanceReportInput,
  type ClassIdParamInput
} from '@/lib/validations/attendance'
import {
  attendanceDbUtils,
  attendanceStatusUtils,
  transformUtils
} from '@/lib/utils/attendance'

/**
 * GET /api/attendance/class/[classId]
 * Get attendance report for a specific class
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { classId: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-class-report', 50, 60 * 1000)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Validate class ID parameter
    const paramValidation = classIdParamSchema.safeParse({ classId: params.classId })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid class ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    const { classId }: ClassIdParamInput = paramValidation.data

    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const validationResult = classAttendanceReportSchema.safeParse({
      ...queryParams,
      classId
    })
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const query: ClassAttendanceReportInput = validationResult.data

    // Verify class exists and get class information
    const classInfo = await prisma.class.findUnique({
      where: { id: classId },
      include: {
        teacher: {
          select: {
            id: true,
            employeeNumber: true,
            firstName: true,
            lastName: true,
            middleName: true,
            email: true
          }
        }
      }
    })

    if (!classInfo) {
      return NextResponse.json(
        {
          success: false,
          error: 'Class not found'
        },
        { status: 404 }
      )
    }

    // Check if user has permission to view this class
    // Teachers can only view their own classes, admins and staff can view all
    if (user.role === 'TEACHER' && classInfo.teacherId !== user.teacherProfileId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Insufficient permissions to view this class'
        },
        { status: 403 }
      )
    }

    // Set date range
    let startDate: Date, endDate: Date

    if (query.date) {
      // Single date report
      startDate = new Date(query.date)
      startDate.setHours(0, 0, 0, 0)
      endDate = new Date(query.date)
      endDate.setHours(23, 59, 59, 999)
    } else {
      // Date range report
      endDate = query.endDate || new Date()
      startDate = query.startDate || new Date(endDate.getTime() - (7 * 24 * 60 * 60 * 1000)) // 7 days ago
    }

    // Get students in this class (by grade level and section)
    const studentsInClass = await prisma.student.findMany({
      where: {
        gradeLevel: classInfo.gradeLevel,
        section: classInfo.section,
        status: 'ACTIVE'
      },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        middleName: true,
        gradeLevel: true,
        section: true
      },
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' }
      ]
    })

    // Get attendance records for these students in the date range
    const attendanceRecords = await prisma.attendance.findMany({
      where: {
        studentId: {
          in: studentsInClass.map(s => s.id)
        },
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        student: {
          select: {
            id: true,
            studentNumber: true,
            firstName: true,
            lastName: true,
            middleName: true
          }
        },
        teacher: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: [
        { date: 'desc' },
        { student: { lastName: 'asc' } },
        { student: { firstName: 'asc' } }
      ]
    })

    // Create attendance matrix (students x dates)
    const dateRange = []
    const currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      dateRange.push(new Date(currentDate))
      currentDate.setDate(currentDate.getDate() + 1)
    }

    const attendanceMatrix = studentsInClass.map(student => {
      const studentAttendance = attendanceRecords.filter(record => 
        record.studentId === student.id
      )

      const dailyAttendance = dateRange.map(date => {
        const dateStr = date.toISOString().split('T')[0]
        const record = studentAttendance.find(att => 
          att.date.toISOString().split('T')[0] === dateStr
        )

        return {
          date: dateStr,
          status: record?.status || null,
          statusDisplay: record ? attendanceStatusUtils.getStatusDisplay(record.status) : 'No Record',
          statusColor: record ? attendanceStatusUtils.getStatusColor(record.status) : 'gray',
          timeIn: record?.timeIn || null,
          timeOut: record?.timeOut || null,
          remarks: record?.remarks || null
        }
      })

      // Calculate student statistics
      const studentStats = {
        total: studentAttendance.length,
        present: studentAttendance.filter(r => r.status === 'PRESENT').length,
        absent: studentAttendance.filter(r => r.status === 'ABSENT').length,
        late: studentAttendance.filter(r => r.status === 'LATE').length,
        excused: studentAttendance.filter(r => r.status === 'EXCUSED').length,
        attendanceRate: 0
      }

      if (studentStats.total > 0) {
        studentStats.attendanceRate = 
          ((studentStats.present + studentStats.late + studentStats.excused) / studentStats.total) * 100
      }

      return {
        student: {
          id: student.id,
          studentNumber: student.studentNumber,
          firstName: student.firstName,
          lastName: student.lastName,
          middleName: student.middleName,
          fullName: `${student.firstName} ${student.lastName}`
        },
        statistics: studentStats,
        dailyAttendance: dailyAttendance
      }
    })

    // Calculate class statistics
    const classStats = await attendanceDbUtils.getAttendanceStats(startDate, endDate, {
      gradeLevel: classInfo.gradeLevel,
      section: classInfo.section
    })

    // Get daily class statistics
    const dailyStats = dateRange.map(date => {
      const dateStr = date.toISOString().split('T')[0]
      const dayRecords = attendanceRecords.filter(record => 
        record.date.toISOString().split('T')[0] === dateStr
      )

      const dayStats = {
        date: dateStr,
        total: dayRecords.length,
        present: dayRecords.filter(r => r.status === 'PRESENT').length,
        absent: dayRecords.filter(r => r.status === 'ABSENT').length,
        late: dayRecords.filter(r => r.status === 'LATE').length,
        excused: dayRecords.filter(r => r.status === 'EXCUSED').length,
        attendanceRate: 0
      }

      if (dayStats.total > 0) {
        dayStats.attendanceRate = 
          ((dayStats.present + dayStats.late + dayStats.excused) / dayStats.total) * 100
      }

      return dayStats
    })

    // Filter absent students if requested
    let reportData = attendanceMatrix
    if (!query.includeAbsent) {
      reportData = attendanceMatrix.filter(student => 
        student.statistics.attendanceRate > 0
      )
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'attendance',
      resourceId: classId,
      details: {
        endpoint: 'class_report',
        classId,
        className: `${classInfo.subjectName} - ${classInfo.gradeLevel}-${classInfo.section}`,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        studentCount: studentsInClass.length,
        recordCount: attendanceRecords.length
      }
    }, request)

    const response = {
      success: true,
      data: {
        class: {
          id: classInfo.id,
          subjectName: classInfo.subjectName,
          gradeLevel: classInfo.gradeLevel,
          section: classInfo.section,
          roomNumber: classInfo.roomNumber,
          teacher: {
            id: classInfo.teacher.id,
            employeeNumber: classInfo.teacher.employeeNumber,
            firstName: classInfo.teacher.firstName,
            lastName: classInfo.teacher.lastName,
            middleName: classInfo.teacher.middleName,
            fullName: `${classInfo.teacher.firstName} ${classInfo.teacher.lastName}`,
            email: classInfo.teacher.email
          }
        },
        summary: classStats,
        dailyStatistics: dailyStats,
        studentAttendance: reportData
      },
      meta: {
        dateRange: {
          start: startDate.toISOString().split('T')[0],
          end: endDate.toISOString().split('T')[0]
        },
        totalStudents: studentsInClass.length,
        totalRecords: attendanceRecords.length,
        format: query.format,
        includeAbsent: query.includeAbsent
      }
    }

    // Return appropriate format
    if (query.format === 'json') {
      return NextResponse.json(response)
    } else {
      // For CSV/PDF formats, you would implement export functionality here
      // For now, return JSON with a note about format
      return NextResponse.json({
        ...response,
        note: `${query.format.toUpperCase()} export functionality would be implemented here`
      })
    }

  } catch (error) {
    console.error('GET /api/attendance/class/[classId] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve class attendance report.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve class attendance report.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve class attendance report.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve class attendance report.' },
    { status: 405 }
  )
}
