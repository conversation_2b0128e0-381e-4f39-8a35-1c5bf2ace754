#!/usr/bin/env tsx

/**
 * SMS System Initialization Script
 * 
 * This script initializes the SMS notification system with:
 * - Default system templates
 * - Default SMS settings
 * - Sample data for testing
 */

import { prisma } from '../lib/db'
import { SMSTemplateService } from '../lib/services/sms-template'
import { SMSCostService } from '../lib/services/sms-cost'
import { TemplateCategory } from '../generated/prisma'

async function initializeSMSSystem() {
  console.log('🚀 Initializing SMS Notification System...')

  try {
    // 1. Initialize default SMS settings
    console.log('📋 Setting up default SMS configuration...')
    await SMSCostService.initializeDefaultSettings()
    console.log('✅ SMS settings initialized')

    // 2. Initialize system templates
    console.log('📝 Creating default SMS templates...')
    await SMSTemplateService.initializeSystemTemplates()
    console.log('✅ System templates created')

    // 3. Verify templates were created
    const templates = await SMSTemplateService.getTemplates({ isSystem: true })
    console.log(`✅ Created ${templates.length} system templates:`)
    templates.forEach(template => {
      console.log(`   - ${template.name} (${template.category})`)
    })

    // 4. Display current settings
    console.log('\n⚙️  Current SMS Settings:')
    const settings = await SMSCostService.getAllSMSSettings()
    Object.entries(settings).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`)
    })

    // 5. Check environment configuration
    console.log('\n🔧 Environment Configuration:')
    console.log(`   SEMAPHORE_API_KEY: ${process.env.SEMAPHORE_API_KEY ? '✅ Set' : '❌ Not set'}`)
    console.log(`   SEMAPHORE_BASE_URL: ${process.env.SEMAPHORE_BASE_URL || 'Not set'}`)
    console.log(`   SEMAPHORE_SENDER_NAME: ${process.env.SEMAPHORE_SENDER_NAME || 'Not set'}`)
    console.log(`   SMS_COST_PER_MESSAGE: ${process.env.SMS_COST_PER_MESSAGE || 'Not set'}`)

    // 6. Create sample blacklist entries (for testing)
    console.log('\n🚫 Creating sample blacklist entries...')
    try {
      await prisma.sMSBlacklist.createMany({
        data: [
          {
            phoneNumber: '639999999999',
            reason: 'Test blacklist entry - do not remove',
            addedBy: null
          }
        ],
        skipDuplicates: true
      })
      console.log('✅ Sample blacklist entries created')
    } catch (error) {
      console.log('ℹ️  Sample blacklist entries already exist')
    }

    // 7. Display available template variables
    console.log('\n📊 Available Template Variables:')
    const variables = SMSTemplateService.getAvailableVariables()
    Object.entries(variables).forEach(([category, vars]) => {
      console.log(`   ${category.toUpperCase()}:`)
      vars.slice(0, 3).forEach(variable => {
        console.log(`     - {${variable.name}}: ${variable.description}`)
      })
      if (vars.length > 3) {
        console.log(`     ... and ${vars.length - 3} more`)
      }
    })

    // 8. Test database connectivity
    console.log('\n🔍 Testing database connectivity...')
    const smsLogCount = await prisma.sMSLog.count()
    const templateCount = await prisma.sMSTemplate.count()
    const blacklistCount = await prisma.sMSBlacklist.count()
    const settingsCount = await prisma.sMSSetting.count()

    console.log(`   SMS Logs: ${smsLogCount}`)
    console.log(`   Templates: ${templateCount}`)
    console.log(`   Blacklist entries: ${blacklistCount}`)
    console.log(`   Settings: ${settingsCount}`)

    // 9. Display next steps
    console.log('\n🎉 SMS System Initialization Complete!')
    console.log('\n📋 Next Steps:')
    console.log('   1. Configure your Semaphore API key in .env file')
    console.log('   2. Test the SMS system using: POST /api/sms/test')
    console.log('   3. Create custom templates via: POST /api/sms/templates')
    console.log('   4. Start sending SMS via: POST /api/sms/send')
    console.log('   5. Monitor costs via: GET /api/sms/costs')

    console.log('\n📚 Documentation:')
    console.log('   - Read SMS_SYSTEM_README.md for detailed usage')
    console.log('   - Check API endpoints documentation')
    console.log('   - Review security best practices')

    console.log('\n🔧 Configuration Tips:')
    console.log('   - Set appropriate daily/monthly budget limits')
    console.log('   - Configure rate limiting based on your needs')
    console.log('   - Test with small batches before large deployments')
    console.log('   - Monitor delivery rates and costs regularly')

  } catch (error) {
    console.error('❌ Error initializing SMS system:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the initialization
if (require.main === module) {
  initializeSMSSystem()
    .then(() => {
      console.log('\n✨ SMS System is ready to use!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Initialization failed:', error)
      process.exit(1)
    })
}

export { initializeSMSSystem }
