import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import { z } from 'zod'
import { smsUtils, smsStatsUtils } from '@/lib/utils/sms'

// SMS notification request schema
const smsNotificationSchema = z.object({
  attendanceIds: z.array(z.string().cuid()).min(1, 'At least one attendance ID is required'),
  force: z.boolean().default(false), // Force send even if already sent
  customMessage: z.string().max(500).optional()
})

// SMS retry schema
const smsRetrySchema = z.object({
  smsLogIds: z.array(z.string().cuid()).min(1, 'At least one SMS log ID is required')
})

/**
 * POST /api/attendance/notifications
 * Send SMS notifications for attendance records
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-sms', 20, 60 * 1000) // 20 SMS requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many SMS requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (ADMIN, TEACHER, and STAFF can send SMS notifications)
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = smsNotificationSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid SMS notification data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { attendanceIds, force, customMessage } = validationResult.data

    // Get attendance records with student information
    const attendanceRecords = await prisma.attendance.findMany({
      where: {
        id: { in: attendanceIds }
      },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            gradeLevel: true,
            section: true,
            guardianName: true,
            guardianContact: true
          }
        },
        smsLogs: {
          select: {
            id: true,
            status: true,
            sentAt: true
          }
        }
      }
    })

    if (attendanceRecords.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No attendance records found'
        },
        { status: 404 }
      )
    }

    // Filter records that need SMS notifications
    const recordsToNotify = attendanceRecords.filter(record => {
      if (force) return true
      
      // Check if SMS already sent successfully
      const successfulSMS = record.smsLogs.some(sms => 
        sms.status === 'SENT' || sms.status === 'DELIVERED'
      )
      
      return !successfulSMS
    })

    if (recordsToNotify.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'All selected attendance records already have SMS notifications sent',
          suggestion: 'Use force=true to resend notifications'
        },
        { status: 400 }
      )
    }

    // Send SMS notifications
    const notificationPromises = recordsToNotify.map(record => 
      smsUtils.sendAttendanceNotification(
        record.studentId,
        record.id,
        record.status,
        record.timeIn
      )
    )

    const results = await Promise.allSettled(notificationPromises)

    // Process results
    const successfulNotifications = []
    const failedNotifications = []

    results.forEach((result, index) => {
      const record = recordsToNotify[index]
      
      if (result.status === 'fulfilled' && result.value.success) {
        successfulNotifications.push({
          attendanceId: record.id,
          studentId: record.studentId,
          studentName: `${record.student.firstName} ${record.student.lastName}`,
          guardianContact: record.student.guardianContact,
          smsLogId: result.value.smsLogId
        })
      } else {
        failedNotifications.push({
          attendanceId: record.id,
          studentId: record.studentId,
          studentName: `${record.student.firstName} ${record.student.lastName}`,
          guardianContact: record.student.guardianContact,
          error: result.status === 'fulfilled' ? result.value.error : result.reason
        })
      }
    })

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'CREATE',
      resource: 'sms_notification',
      details: {
        attendanceIds,
        totalRecords: attendanceRecords.length,
        recordsToNotify: recordsToNotify.length,
        successful: successfulNotifications.length,
        failed: failedNotifications.length,
        force,
        customMessage: !!customMessage
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalRequested: attendanceIds.length,
          totalFound: attendanceRecords.length,
          totalToNotify: recordsToNotify.length,
          successful: successfulNotifications.length,
          failed: failedNotifications.length
        },
        successfulNotifications,
        failedNotifications
      },
      message: `SMS notifications sent: ${successfulNotifications.length} successful, ${failedNotifications.length} failed`
    }, { status: 201 })

  } catch (error) {
    console.error('POST /api/attendance/notifications error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/attendance/notifications
 * Get SMS notification statistics and failed messages
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-sms-status', 50, 60 * 1000)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const url = new URL(request.url)
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')
    const includeFailed = url.searchParams.get('includeFailed') === 'true'

    // Default to last 7 days if no date range provided
    const end = endDate ? new Date(endDate) : new Date()
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - (7 * 24 * 60 * 60 * 1000))

    // Get SMS statistics
    const smsStats = await smsStatsUtils.getSMSStats(start, end)

    // Get failed SMS messages if requested
    let failedMessages = []
    if (includeFailed) {
      failedMessages = await smsStatsUtils.getFailedSMS(50)
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'sms_notification',
      details: {
        endpoint: 'notifications_status',
        dateRange: {
          start: start.toISOString(),
          end: end.toISOString()
        },
        includeFailed
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        statistics: smsStats,
        failedMessages: includeFailed ? failedMessages : undefined,
        period: {
          startDate: start.toISOString().split('T')[0],
          endDate: end.toISOString().split('T')[0]
        }
      }
    })

  } catch (error) {
    console.error('GET /api/attendance/notifications error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/attendance/notifications
 * Retry failed SMS notifications
 */
export async function PUT(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-sms-retry', 10, 60 * 1000) // 10 retries per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many retry requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (ADMIN and STAFF can retry SMS)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = smsRetrySchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid SMS retry data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const { smsLogIds } = validationResult.data

    // Retry failed SMS messages
    const retryPromises = smsLogIds.map(smsLogId => 
      smsUtils.retryFailedSMS(smsLogId)
    )

    const results = await Promise.allSettled(retryPromises)

    // Process results
    const successfulRetries = []
    const failedRetries = []

    results.forEach((result, index) => {
      const smsLogId = smsLogIds[index]
      
      if (result.status === 'fulfilled' && result.value.success) {
        successfulRetries.push({
          smsLogId,
          message: result.value.message
        })
      } else {
        failedRetries.push({
          smsLogId,
          error: result.status === 'fulfilled' ? result.value.error : result.reason
        })
      }
    })

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'UPDATE',
      resource: 'sms_notification',
      details: {
        action: 'retry_failed',
        smsLogIds,
        successful: successfulRetries.length,
        failed: failedRetries.length
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalRetried: smsLogIds.length,
          successful: successfulRetries.length,
          failed: failedRetries.length
        },
        successfulRetries,
        failedRetries
      },
      message: `SMS retry completed: ${successfulRetries.length} successful, ${failedRetries.length} failed`
    })

  } catch (error) {
    console.error('PUT /api/attendance/notifications error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send notifications, GET to check status, or PUT to retry failed messages.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send notifications, GET to check status, or PUT to retry failed messages.' },
    { status: 405 }
  )
}
