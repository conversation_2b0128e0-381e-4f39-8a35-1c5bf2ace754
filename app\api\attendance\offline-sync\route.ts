import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  offlineSyncSchema,
  type OfflineSyncInput
} from '@/lib/validations/attendance'
import {
  offlineQueueUtils,
  queueConflictUtils,
  offlineSyncStatsUtils
} from '@/lib/utils/offline-queue'

/**
 * POST /api/attendance/offline-sync
 * Process offline attendance queue items
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting - allow more frequent offline syncs
    const rateLimitResult = await rateLimit(request, 'attendance-offline-sync', 20, 60 * 1000) // 20 syncs per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many sync requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (ADMIN, TEACHER, and STAFF can sync offline data)
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = offlineSyncSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid offline sync data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const syncData: OfflineSyncInput = validationResult.data

    // Get teacher ID for attendance records
    let teacherId = user.teacherProfileId
    if (!teacherId && user.role === 'TEACHER') {
      // Try to find teacher profile by user email
      const teacher = await prisma.teacher.findUnique({
        where: { email: user.email },
        select: { id: true }
      })
      teacherId = teacher?.id
    }

    // If no teacher profile found, use a default system teacher
    if (!teacherId) {
      const systemTeacher = await prisma.teacher.findFirst({
        where: { employeeNumber: 'SYSTEM' }
      })
      
      if (!systemTeacher) {
        const newSystemTeacher = await prisma.teacher.create({
          data: {
            employeeNumber: 'SYSTEM',
            firstName: 'System',
            lastName: 'Offline Sync',
            contactNumber: '00000000000',
            email: '<EMAIL>',
            subjectsHandled: JSON.stringify(['Attendance'])
          }
        })
        teacherId = newSystemTeacher.id
      } else {
        teacherId = systemTeacher.id
      }
    }

    // Validate queue items before processing
    const validationResults = await offlineQueueUtils.validateQueueItems(syncData.items)
    const validItems = syncData.items.filter((_, index) => 
      validationResults[index].valid
    )
    const invalidItems = validationResults.filter(result => !result.valid)

    if (validItems.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No valid items to sync',
          validationErrors: invalidItems
        },
        { status: 400 }
      )
    }

    // Resolve conflicts if multiple items exist for same student/date
    const resolvedItems = await queueConflictUtils.resolveConflicts(validItems)

    // Process the resolved queue items
    const syncResults = await offlineQueueUtils.processBatchQueue(resolvedItems, teacherId!)

    // Get sync statistics
    const syncStats = {
      totalSubmitted: syncData.items.length,
      validItems: validItems.length,
      invalidItems: invalidItems.length,
      conflictsResolved: syncData.items.length - resolvedItems.length,
      processed: syncResults.success,
      failed: syncResults.failed,
      deviceId: syncData.deviceId,
      syncTimestamp: syncData.syncTimestamp || new Date()
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'CREATE',
      resource: 'attendance',
      details: {
        method: 'offline_sync',
        deviceId: syncData.deviceId,
        syncStats,
        teacherId,
        itemCount: syncData.items.length,
        validItemCount: validItems.length,
        resolvedItemCount: resolvedItems.length
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        syncStats,
        results: syncResults.results,
        validationErrors: invalidItems,
        conflictResolutions: syncData.items.length - resolvedItems.length
      },
      message: `Offline sync completed: ${syncResults.success} successful, ${syncResults.failed} failed`
    }, { status: 201 })

  } catch (error) {
    console.error('POST /api/attendance/offline-sync error:', error)
    
    // Log error for debugging
    try {
      const token = request.cookies.get('access-token')?.value ||
                    request.headers.get('authorization')?.replace('Bearer ', '')
      const user = token ? await getUserFromToken(token) : null
      
      await auditHelpers.log({
        userId: user?.id,
        action: 'CREATE',
        resource: 'attendance',
        details: {
          method: 'offline_sync',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        }
      }, request)
    } catch (auditError) {
      console.error('Audit logging failed:', auditError)
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to process offline sync'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/attendance/offline-sync
 * Get offline sync statistics and status
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-offline-sync-status', 50, 60 * 1000)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Parse query parameters
    const url = new URL(request.url)
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')
    const queueIds = url.searchParams.get('queueIds')

    // Default to last 7 days if no date range provided
    const end = endDate ? new Date(endDate) : new Date()
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - (7 * 24 * 60 * 60 * 1000))

    // Get offline sync statistics
    const syncStats = await offlineSyncStatsUtils.getSyncStats(start, end)

    // Check processed queue items if requested
    let processedItems = []
    if (queueIds) {
      const queueIdArray = queueIds.split(',')
      processedItems = await offlineQueueUtils.getProcessedItems(queueIdArray)
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'attendance',
      details: {
        endpoint: 'offline_sync_status',
        dateRange: {
          start: start.toISOString(),
          end: end.toISOString()
        },
        queueIdsChecked: queueIds ? queueIds.split(',').length : 0
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        syncStatistics: syncStats,
        processedQueueItems: processedItems,
        period: {
          startDate: start.toISOString().split('T')[0],
          endDate: end.toISOString().split('T')[0]
        }
      }
    })

  } catch (error) {
    console.error('GET /api/attendance/offline-sync error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sync offline data or GET to check sync status.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sync offline data or GET to check sync status.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to sync offline data or GET to check sync status.' },
    { status: 405 }
  )
}
