# QR-Code Based Student Attendance API System

## Overview

This comprehensive attendance processing API system provides QR code scanning, manual entry, analytics, and SMS notification capabilities for the QR-Code Based Student Attendance and Monitoring System (QRSAMS).

## Features

- ✅ QR code validation and processing
- ✅ Real-time attendance recording
- ✅ Manual attendance entry
- ✅ Bulk attendance operations
- ✅ Comprehensive attendance analytics
- ✅ SMS notification integration
- ✅ Offline queue processing
- ✅ Data export capabilities
- ✅ Attendance history tracking
- ✅ Class attendance reports

## API Endpoints

### 1. QR Code Scanning
**POST** `/api/attendance/scan`

Process QR code scans for attendance recording with duplicate prevention and real-time validation.

**Request Body:**
```json
{
  "qrData": "QRSAMS_student123_1234567890_abc123",
  "location": "Main Gate",
  "notes": "Morning entry",
  "timestamp": "2024-01-15T08:30:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "attendance_id",
    "studentId": "student_id",
    "status": "PRESENT",
    "timeIn": "2024-01-15T08:30:00Z",
    "student": {
      "firstName": "John",
      "lastName": "Doe",
      "studentNumber": "2024-001",
      "gradeLevel": "G10",
      "section": "A"
    }
  },
  "message": "Attendance recorded successfully - Present"
}
```

### 2. Manual Attendance Entry
**POST** `/api/attendance/manual`

Manual attendance entry with proper validation and status calculation.

**Request Body:**
```json
{
  "studentId": "student_id",
  "status": "PRESENT",
  "timeIn": "2024-01-15T08:30:00Z",
  "timeOut": "2024-01-15T15:30:00Z",
  "remarks": "Manual entry by teacher",
  "date": "2024-01-15"
}
```

### 3. Today's Attendance Summary
**GET** `/api/attendance/today`

Get today's attendance summary with optional filtering by grade level and section.

**Query Parameters:**
- `gradeLevel` (optional): Filter by grade level
- `section` (optional): Filter by section
- `includeDetails` (optional): Include detailed attendance records

**Response:**
```json
{
  "success": true,
  "data": {
    "date": "2024-01-15",
    "summary": {
      "total": 150,
      "present": 120,
      "absent": 20,
      "late": 8,
      "excused": 2,
      "attendanceRate": 86.67
    },
    "gradeBreakdown": [...],
    "recentActivity": [...]
  }
}
```

### 4. Student Attendance History
**GET** `/api/attendance/student/[id]`

Get comprehensive attendance history for a specific student.

**Query Parameters:**
- `startDate` (optional): Start date for history
- `endDate` (optional): End date for history
- `status` (optional): Filter by attendance status
- `page` (optional): Page number for pagination
- `limit` (optional): Records per page

### 5. Class Attendance Report
**GET** `/api/attendance/class/[classId]`

Get detailed attendance report for a specific class with attendance matrix.

**Query Parameters:**
- `date` (optional): Specific date for report
- `startDate` (optional): Start date for date range
- `endDate` (optional): End date for date range
- `includeAbsent` (optional): Include absent students
- `format` (optional): Response format (json, csv, pdf)

### 6. Attendance Management
**GET/PUT/DELETE** `/api/attendance/[id]`

Manage individual attendance records with proper authorization.

**PUT Request Body:**
```json
{
  "status": "LATE",
  "timeIn": "2024-01-15T08:45:00Z",
  "remarks": "Updated by teacher"
}
```

### 7. Attendance Analytics
**GET** `/api/attendance/analytics`

Comprehensive attendance analytics with insights and trends.

**Query Parameters:**
- `startDate` (required): Analysis start date
- `endDate` (required): Analysis end date
- `gradeLevel` (optional): Filter by grade level
- `section` (optional): Filter by section
- `groupBy` (optional): Group data by day/week/month/grade/section
- `metrics` (optional): Specific metrics to include

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "total": 1500,
      "attendanceRate": 87.5
    },
    "trends": [...],
    "gradeBreakdown": [...],
    "atRiskStudents": [...],
    "insights": [...]
  }
}
```

### 8. Bulk Operations
**POST** `/api/attendance/bulk-update`

Bulk attendance operations with transaction support.

**Request Body:**
```json
{
  "updates": [
    {
      "studentId": "student1_id",
      "status": "PRESENT",
      "timeIn": "2024-01-15T08:30:00Z",
      "date": "2024-01-15"
    }
  ],
  "teacherId": "teacher_id",
  "overwriteExisting": false
}
```

### 9. Data Export
**GET** `/api/attendance/export`

Export attendance data in various formats (CSV, Excel, PDF).

**Query Parameters:**
- `format` (required): Export format (csv, excel, pdf)
- `startDate` (required): Export start date
- `endDate` (required): Export end date
- `gradeLevel` (optional): Filter by grade level
- `section` (optional): Filter by section
- `includeStudentDetails` (optional): Include guardian information
- `includeStatistics` (optional): Include summary statistics

### 10. Offline Queue Processing
**POST/GET** `/api/attendance/offline-sync`

Handle offline attendance data synchronization.

**POST Request Body:**
```json
{
  "items": [
    {
      "studentId": "student_id",
      "action": "PRESENT",
      "timestamp": "2024-01-15T08:30:00Z",
      "location": "Main Gate",
      "queueId": "offline_item_123"
    }
  ],
  "deviceId": "scanner_001",
  "syncTimestamp": "2024-01-15T09:00:00Z"
}
```

### 11. SMS Notifications
**POST/GET/PUT** `/api/attendance/notifications`

Manage SMS notifications for attendance events.

**POST Request Body:**
```json
{
  "attendanceIds": ["attendance_id_1", "attendance_id_2"],
  "force": false,
  "customMessage": "Custom notification message"
}
```

## Authentication & Authorization

All endpoints require authentication via:
- Cookie: `access-token`
- Header: `Authorization: Bearer <token>`

### Role-based Access:
- **ADMIN**: Full access to all endpoints
- **TEACHER**: Access to own classes and students
- **STAFF**: Access to attendance operations and reports

## Rate Limiting

Different endpoints have specific rate limits:
- QR Scanning: 30 requests/minute
- Manual Entry: 50 requests/minute
- Analytics: 30 requests/minute
- Bulk Operations: 5 requests/minute
- SMS Operations: 20 requests/minute

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "details": [...] // Additional error details when available
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict
- `429`: Too Many Requests
- `500`: Internal Server Error

## Data Validation

All inputs are validated using Zod schemas with comprehensive error messages:
- Date validation with reasonable ranges
- Time range validation
- Student ID format validation
- Phone number format validation (Philippine format)
- QR code format validation

## SMS Integration

The system includes comprehensive SMS notification capabilities:
- Automatic notifications for attendance events
- Customizable message templates
- Delivery status tracking
- Failed message retry functionality
- Bulk notification support

## Offline Support

Robust offline queue processing:
- Conflict resolution for duplicate entries
- Validation before processing
- Batch synchronization
- Statistics tracking
- Device identification

## Security Features

- Rate limiting on all endpoints
- Input validation and sanitization
- SQL injection prevention
- Authentication token validation
- Audit logging for all operations
- Role-based access control

## Database Optimization

- Indexed queries for performance
- Transaction support for data integrity
- Efficient pagination
- Optimized analytics queries
- Proper foreign key relationships

## Monitoring & Analytics

Comprehensive analytics capabilities:
- Daily, weekly, monthly trends
- Grade/section breakdowns
- At-risk student identification
- Peak attendance hour analysis
- Weekly pattern analysis
- Automated insights generation

## TypeScript Support

Full TypeScript integration with:
- Comprehensive type definitions
- Input/output type safety
- Validation schema types
- Database model types
- API response types

This attendance API system provides a robust, scalable, and feature-rich solution for managing student attendance with QR code technology, real-time processing, and comprehensive analytics capabilities.
