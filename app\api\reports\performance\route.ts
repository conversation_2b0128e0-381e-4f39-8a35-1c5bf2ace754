import { NextRequest, NextResponse } from 'next/server'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { PerformanceMonitorService } from '@/lib/services/performance-monitor'
import { ReportCacheService } from '@/lib/services/report-cache'
import { FileStorageService } from '@/lib/services/file-storage'
import { z } from 'zod'

// Validation schema for performance operations
const performanceActionSchema = z.object({
  action: z.enum(['optimize_cache', 'clear_cache', 'preload_cache', 'cleanup_storage', 'get_metrics']),
  parameters: z.record(z.any()).optional()
})

/**
 * GET /api/reports/performance - Get system performance metrics
 */
export async function GET(request: NextRequest) {
  try {
    // Authentication
    const user = await getUserFromToken(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin permissions
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const operation = searchParams.get('operation')
    const startTime = searchParams.get('startTime') ? new Date(searchParams.get('startTime')!) : undefined
    const endTime = searchParams.get('endTime') ? new Date(searchParams.get('endTime')!) : undefined

    const performanceMonitor = PerformanceMonitorService.getInstance()
    const cacheService = ReportCacheService.getInstance()
    const fileStorage = FileStorageService.getInstance()

    // Get system health
    const systemHealth = await performanceMonitor.getSystemHealth()

    // Get performance metrics
    const metrics = performanceMonitor.getMetrics(operation || undefined, startTime, endTime)

    // Get performance statistics
    const statistics = performanceMonitor.getStatistics(operation || undefined)

    // Get alerts
    const activeAlerts = performanceMonitor.getAlerts(false)
    const resolvedAlerts = performanceMonitor.getAlerts(true).slice(0, 10) // Last 10 resolved

    // Get cache metrics
    const cacheMetrics = cacheService.getCacheMetrics()
    const cacheStats = await cacheService.getStats()

    // Get storage statistics
    const storageStats = await fileStorage.getStorageStats()

    return NextResponse.json({
      success: true,
      systemHealth,
      metrics: {
        recent: metrics.slice(0, 100), // Last 100 metrics
        statistics
      },
      alerts: {
        active: activeAlerts,
        resolved: resolvedAlerts
      },
      cache: {
        metrics: cacheMetrics,
        stats: cacheStats
      },
      storage: storageStats,
      recommendations: generatePerformanceRecommendations(systemHealth, statistics, cacheStats)
    })

  } catch (error) {
    console.error('Performance metrics fetch error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/reports/performance - Execute performance optimization actions
 */
export async function POST(request: NextRequest) {
  try {
    // Authentication
    const user = await getUserFromToken(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin permissions
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = performanceActionSchema.parse(body)

    const performanceMonitor = PerformanceMonitorService.getInstance()
    const cacheService = ReportCacheService.getInstance()
    const fileStorage = FileStorageService.getInstance()

    let result: any = {}

    switch (validatedData.action) {
      case 'optimize_cache':
        const maxEntries = validatedData.parameters?.maxEntries || 1000
        const deletedEntries = await cacheService.optimizeCache(maxEntries)
        result = {
          action: 'optimize_cache',
          deletedEntries,
          message: `Optimized cache, removed ${deletedEntries} entries`
        }
        break

      case 'clear_cache':
        await cacheService.clearAllCaches()
        result = {
          action: 'clear_cache',
          message: 'All caches cleared successfully'
        }
        break

      case 'preload_cache':
        await cacheService.preloadHotCache()
        result = {
          action: 'preload_cache',
          message: 'Hot cache entries preloaded into memory'
        }
        break

      case 'cleanup_storage':
        const cleanupResult = await fileStorage.cleanupOldFiles()
        result = {
          action: 'cleanup_storage',
          deletedFiles: cleanupResult.deletedCount,
          freedSpace: cleanupResult.freedSpace,
          message: `Cleaned up ${cleanupResult.deletedCount} files, freed ${FileStorageService.formatFileSize(cleanupResult.freedSpace)}`
        }
        break

      case 'get_metrics':
        const systemHealth = await performanceMonitor.getSystemHealth()
        const statistics = performanceMonitor.getStatistics()
        result = {
          action: 'get_metrics',
          systemHealth,
          statistics,
          message: 'Performance metrics retrieved'
        }
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    // Log the action
    await auditHelpers.log({
      userId: user.id,
      action: 'PERFORMANCE_ACTION',
      resource: 'system',
      details: {
        action: validatedData.action,
        parameters: validatedData.parameters,
        result
      }
    }, request)

    return NextResponse.json({
      success: true,
      result,
      timestamp: new Date()
    })

  } catch (error) {
    console.error('Performance action error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          details: error.errors
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/reports/performance - Resolve performance alerts
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authentication
    const user = await getUserFromToken(request)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check admin permissions
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { alertId, alertIds } = body

    const performanceMonitor = PerformanceMonitorService.getInstance()

    if (alertId) {
      // Resolve single alert
      performanceMonitor.resolveAlert(alertId)
    } else if (alertIds && Array.isArray(alertIds)) {
      // Resolve multiple alerts
      alertIds.forEach(id => performanceMonitor.resolveAlert(id))
    } else {
      return NextResponse.json(
        { success: false, error: 'Alert ID or IDs required' },
        { status: 400 }
      )
    }

    // Log the action
    await auditHelpers.log({
      userId: user.id,
      action: 'RESOLVE_ALERTS',
      resource: 'performance_alert',
      details: {
        alertId,
        alertIds,
        resolvedCount: alertIds ? alertIds.length : 1
      }
    }, request)

    return NextResponse.json({
      success: true,
      message: `Resolved ${alertIds ? alertIds.length : 1} alert(s)`
    })

  } catch (error) {
    console.error('Alert resolution error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Helper function to generate performance recommendations
function generatePerformanceRecommendations(
  systemHealth: any,
  statistics: any,
  cacheStats: any
): string[] {
  const recommendations: string[] = []

  // Memory recommendations
  if (systemHealth.memoryUsage.percentage > 80) {
    recommendations.push('Consider increasing server memory or optimizing memory usage')
  }

  // Response time recommendations
  if (statistics.averageDuration > 5000) {
    recommendations.push('Average response time is high - consider optimizing database queries or enabling caching')
  }

  // Cache recommendations
  if (cacheStats.hitRate < 70) {
    recommendations.push('Cache hit rate is low - consider preloading frequently accessed data')
  }

  // Error rate recommendations
  if (statistics.successRate < 95) {
    recommendations.push('Success rate is below 95% - review error logs and fix recurring issues')
  }

  // Queue recommendations
  if (systemHealth.queuedReports > 20) {
    recommendations.push('Report queue is large - consider increasing concurrent processing capacity')
  }

  // Storage recommendations
  if (systemHealth.diskUsage.percentage > 80) {
    recommendations.push('Disk usage is high - consider archiving old reports or increasing storage capacity')
  }

  // General recommendations
  if (recommendations.length === 0) {
    recommendations.push('System performance is good - continue monitoring for optimal operation')
  }

  return recommendations
}
