import { prisma } from '@/lib/db'
import { 
  SMSTemplate, 
  CreateSMSTemplateInput, 
  UpdateSMSTemplateInput,
  TemplateVariable,
  TemplateVariables
} from '@/lib/types/sms'
import { TemplateCategory } from '../../generated/prisma'

export class SMSTemplateService {
  
  /**
   * Get all available template variables
   */
  static getAvailableVariables(): TemplateVariables {
    return {
      student: [
        { name: 'studentName', description: 'Full name of the student', example: '<PERSON>', required: false },
        { name: 'firstName', description: 'First name of the student', example: '<PERSON>', required: false },
        { name: 'lastName', description: 'Last name of the student', example: '<PERSON><PERSON> Cruz', required: false },
        { name: 'student<PERSON><PERSON><PERSON>', description: 'Student ID number', example: '2024-001', required: false },
        { name: 'gradeLevel', description: 'Grade level', example: 'Grade 10', required: false },
        { name: 'section', description: 'Class section', example: 'A', required: false },
        { name: 'gradeSection', description: 'Combined grade and section', example: 'Grade 10-A', required: false },
        { name: 'guardianName', description: 'Name of the guardian', example: '<PERSON>', required: false },
        { name: 'guardianContact', description: 'Guardian contact number', example: '09123456789', required: false }
      ],
      attendance: [
        { name: 'attendanceStatus', description: 'Attendance status', example: 'Present', required: false },
        { name: 'timeIn', description: 'Time student arrived', example: '7:30 AM', required: false },
        { name: 'timeOut', description: 'Time student left', example: '4:30 PM', required: false },
        { name: 'date', description: 'Date of attendance', example: 'January 15, 2024', required: false },
        { name: 'dateShort', description: 'Short date format', example: '01/15/2024', required: false },
        { name: 'dayOfWeek', description: 'Day of the week', example: 'Monday', required: false }
      ],
      school: [
        { name: 'schoolName', description: 'Name of the school', example: 'Tanauan School of Arts and Trade', required: false },
        { name: 'schoolShortName', description: 'Short school name', example: 'TSAT', required: false },
        { name: 'schoolAddress', description: 'School address', example: 'Brgy. Cabuyan, Tanauan, Leyte', required: false },
        { name: 'schoolPhone', description: 'School contact number', example: '+63 53 325 1234', required: false },
        { name: 'academicYear', description: 'Current academic year', example: '2024-2025', required: false },
        { name: 'semester', description: 'Current semester', example: 'First Semester', required: false }
      ],
      general: [
        { name: 'currentTime', description: 'Current time', example: '2:30 PM', required: false },
        { name: 'currentDate', description: 'Current date', example: 'January 15, 2024', required: false },
        { name: 'currentDateShort', description: 'Current date (short)', example: '01/15/2024', required: false },
        { name: 'currentDay', description: 'Current day of week', example: 'Monday', required: false }
      ]
    }
  }

  /**
   * Get default system templates
   */
  static getDefaultTemplates(): Array<Omit<SMSTemplate, 'id' | 'createdAt' | 'updatedAt'>> {
    return [
      {
        name: 'Student Arrival',
        category: TemplateCategory.ATTENDANCE,
        subject: 'Student Arrival Notification',
        content: 'Good day {guardianName}! Your child {studentName} ({gradeSection}) has arrived at school at {timeIn} on {date}. Have a great day!',
        variables: ['guardianName', 'studentName', 'gradeSection', 'timeIn', 'date'],
        isActive: true,
        isSystem: true,
        description: 'Notification sent when student arrives at school',
        createdBy: null
      },
      {
        name: 'Student Late Arrival',
        category: TemplateCategory.ATTENDANCE,
        subject: 'Late Arrival Notification',
        content: 'Dear {guardianName}, your child {studentName} ({gradeSection}) arrived late at school at {timeIn} on {date}. Please ensure punctuality. Thank you.',
        variables: ['guardianName', 'studentName', 'gradeSection', 'timeIn', 'date'],
        isActive: true,
        isSystem: true,
        description: 'Notification sent when student arrives late',
        createdBy: null
      },
      {
        name: 'Student Absence',
        category: TemplateCategory.ATTENDANCE,
        subject: 'Absence Notification',
        content: 'Dear {guardianName}, your child {studentName} ({gradeSection}) is marked absent today ({date}). If this is incorrect, please contact the school. Thank you.',
        variables: ['guardianName', 'studentName', 'gradeSection', 'date'],
        isActive: true,
        isSystem: true,
        description: 'Notification sent when student is absent',
        createdBy: null
      },
      {
        name: 'General Announcement',
        category: TemplateCategory.ANNOUNCEMENT,
        subject: 'School Announcement',
        content: 'Dear Parents/Guardians, {schoolName} announces: {message}. For inquiries, contact us at {schoolPhone}. Thank you.',
        variables: ['schoolName', 'message', 'schoolPhone'],
        isActive: true,
        isSystem: true,
        description: 'Template for general school announcements',
        createdBy: null
      },
      {
        name: 'Event Reminder',
        category: TemplateCategory.REMINDER,
        subject: 'Event Reminder',
        content: 'Reminder: {eventName} is scheduled on {eventDate} at {eventTime}. Please ensure your child {studentName} is prepared. - {schoolShortName}',
        variables: ['eventName', 'eventDate', 'eventTime', 'studentName', 'schoolShortName'],
        isActive: true,
        isSystem: true,
        description: 'Template for event reminders',
        createdBy: null
      },
      {
        name: 'Emergency Alert',
        category: TemplateCategory.ALERT,
        subject: 'Emergency Alert',
        content: 'URGENT: {alertMessage}. Please contact {schoolPhone} for more information. - {schoolName}',
        variables: ['alertMessage', 'schoolPhone', 'schoolName'],
        isActive: true,
        isSystem: true,
        description: 'Template for emergency alerts',
        createdBy: null
      }
    ]
  }

  /**
   * Create a new SMS template
   */
  static async createTemplate(
    input: CreateSMSTemplateInput,
    createdBy?: string
  ): Promise<SMSTemplate> {
    // Validate template content
    const validation = this.validateTemplate(input.content, input.variables)
    if (!validation.isValid) {
      throw new Error(`Template validation failed: ${validation.errors.join(', ')}`)
    }

    const template = await prisma.sMSTemplate.create({
      data: {
        name: input.name,
        category: input.category,
        subject: input.subject,
        content: input.content,
        variables: JSON.stringify(input.variables),
        description: input.description,
        createdBy
      }
    })

    return {
      ...template,
      variables: JSON.parse(template.variables)
    }
  }

  /**
   * Update an existing SMS template
   */
  static async updateTemplate(
    id: string,
    input: UpdateSMSTemplateInput,
    updatedBy?: string
  ): Promise<SMSTemplate> {
    const existingTemplate = await prisma.sMSTemplate.findUnique({
      where: { id }
    })

    if (!existingTemplate) {
      throw new Error('Template not found')
    }

    if (existingTemplate.isSystem) {
      throw new Error('System templates cannot be modified')
    }

    // Validate template if content or variables are being updated
    if (input.content || input.variables) {
      const content = input.content || existingTemplate.content
      const variables = input.variables || JSON.parse(existingTemplate.variables)
      
      const validation = this.validateTemplate(content, variables)
      if (!validation.isValid) {
        throw new Error(`Template validation failed: ${validation.errors.join(', ')}`)
      }
    }

    const updateData: any = { ...input }
    if (input.variables) {
      updateData.variables = JSON.stringify(input.variables)
    }

    const template = await prisma.sMSTemplate.update({
      where: { id },
      data: updateData
    })

    return {
      ...template,
      variables: JSON.parse(template.variables)
    }
  }

  /**
   * Get template by ID
   */
  static async getTemplate(id: string): Promise<SMSTemplate | null> {
    const template = await prisma.sMSTemplate.findUnique({
      where: { id }
    })

    if (!template) return null

    return {
      ...template,
      variables: JSON.parse(template.variables)
    }
  }

  /**
   * Get all templates with optional filtering
   */
  static async getTemplates(filters?: {
    category?: TemplateCategory
    isActive?: boolean
    isSystem?: boolean
    createdBy?: string
  }): Promise<SMSTemplate[]> {
    const templates = await prisma.sMSTemplate.findMany({
      where: filters,
      orderBy: [
        { isSystem: 'desc' },
        { category: 'asc' },
        { name: 'asc' }
      ]
    })

    return templates.map(template => ({
      ...template,
      variables: JSON.parse(template.variables)
    }))
  }

  /**
   * Delete a template
   */
  static async deleteTemplate(id: string): Promise<void> {
    const template = await prisma.sMSTemplate.findUnique({
      where: { id }
    })

    if (!template) {
      throw new Error('Template not found')
    }

    if (template.isSystem) {
      throw new Error('System templates cannot be deleted')
    }

    await prisma.sMSTemplate.delete({
      where: { id }
    })
  }

  /**
   * Render template with variables
   */
  static renderTemplate(
    content: string,
    variables: Record<string, string>
  ): string {
    let rendered = content

    // Replace all variables in the format {variableName}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{${key}\\}`, 'g')
      rendered = rendered.replace(regex, value || '')
    })

    // Clean up any remaining unreplaced variables
    rendered = rendered.replace(/\{[^}]+\}/g, '')

    return rendered.trim()
  }

  /**
   * Validate template content and variables
   */
  static validateTemplate(
    content: string,
    variables: string[]
  ): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = []
    const warnings: string[] = []

    // Check if content is not empty
    if (!content.trim()) {
      errors.push('Template content cannot be empty')
    }

    // Check content length
    if (content.length > 1600) {
      errors.push('Template content exceeds maximum length of 1600 characters')
    }

    // Extract variables from content
    const contentVariables = content.match(/\{([^}]+)\}/g) || []
    const contentVariableNames = contentVariables.map(v => v.slice(1, -1))

    // Check if all content variables are declared
    const undeclaredVariables = contentVariableNames.filter(v => !variables.includes(v))
    if (undeclaredVariables.length > 0) {
      warnings.push(`Undeclared variables in content: ${undeclaredVariables.join(', ')}`)
    }

    // Check if all declared variables are used
    const unusedVariables = variables.filter(v => !contentVariableNames.includes(v))
    if (unusedVariables.length > 0) {
      warnings.push(`Declared but unused variables: ${unusedVariables.join(', ')}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * Initialize default system templates
   */
  static async initializeSystemTemplates(): Promise<void> {
    const defaultTemplates = this.getDefaultTemplates()

    for (const templateData of defaultTemplates) {
      const existing = await prisma.sMSTemplate.findFirst({
        where: { name: templateData.name, isSystem: true }
      })

      if (!existing) {
        await prisma.sMSTemplate.create({
          data: {
            ...templateData,
            variables: JSON.stringify(templateData.variables)
          }
        })
      }
    }
  }
}
