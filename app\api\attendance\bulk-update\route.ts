import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  bulkAttendanceUpdateSchema,
  validateTimeRange,
  validateAttendanceDate,
  type BulkAttendanceUpdateInput
} from '@/lib/validations/attendance'
import {
  attendanceDbUtils,
  attendanceStatusUtils,
  transformUtils
} from '@/lib/utils/attendance'
import { smsUtils } from '@/lib/utils/sms'

/**
 * POST /api/attendance/bulk-update
 * Bulk update attendance records with transaction support
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting - more restrictive for bulk operations
    const rateLimitResult = await rateLimit(request, 'attendance-bulk-update', 5, 60 * 1000) // 5 bulk operations per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many bulk update requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (ADMIN, TEACHER, and STAFF can bulk update attendance)
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = bulkAttendanceUpdateSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid bulk update data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const bulkData: BulkAttendanceUpdateInput = validationResult.data

    // Validate each update item
    const validationErrors = []
    for (let i = 0; i < bulkData.updates.length; i++) {
      const update = bulkData.updates[i]
      
      // Validate date
      const attendanceDate = update.date || new Date()
      const dateValidation = validateAttendanceDate(attendanceDate)
      if (!dateValidation.valid) {
        validationErrors.push({
          index: i,
          studentId: update.studentId,
          error: dateValidation.error
        })
        continue
      }

      // Validate time range
      if (update.timeIn && update.timeOut) {
        const timeValidation = validateTimeRange(update.timeIn, update.timeOut)
        if (!timeValidation.valid) {
          validationErrors.push({
            index: i,
            studentId: update.studentId,
            error: timeValidation.error
          })
        }
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation errors in bulk update data',
          validationErrors
        },
        { status: 400 }
      )
    }

    // Verify all students exist and are active
    const studentIds = bulkData.updates.map(update => update.studentId)
    const students = await prisma.student.findMany({
      where: {
        id: { in: studentIds },
        status: 'ACTIVE'
      },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        status: true
      }
    })

    const foundStudentIds = new Set(students.map(s => s.id))
    const missingStudents = studentIds.filter(id => !foundStudentIds.has(id))

    if (missingStudents.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Some students not found or inactive',
          missingStudents
        },
        { status: 400 }
      )
    }

    // Check for existing attendance records if not overwriting
    const existingChecks = []
    if (!bulkData.overwriteExisting) {
      for (const update of bulkData.updates) {
        const attendanceDate = update.date || new Date()
        const existing = await attendanceDbUtils.checkAttendanceExists(
          update.studentId,
          attendanceDate
        )
        if (existing) {
          existingChecks.push({
            studentId: update.studentId,
            date: attendanceDate.toISOString().split('T')[0],
            existingId: existing.id
          })
        }
      }

      if (existingChecks.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Attendance records already exist for some students',
            existingRecords: existingChecks,
            suggestion: 'Set overwriteExisting to true to update existing records'
          },
          { status: 409 }
        )
      }
    }

    // Process bulk updates in transaction
    const results = await prisma.$transaction(async (tx) => {
      const processedUpdates = []
      const errors = []
      const smsNotifications = []

      for (let i = 0; i < bulkData.updates.length; i++) {
        const update = bulkData.updates[i]
        const attendanceDate = update.date || new Date()

        try {
          // Check if attendance exists
          const existing = await tx.attendance.findFirst({
            where: {
              studentId: update.studentId,
              date: {
                gte: new Date(attendanceDate.getFullYear(), attendanceDate.getMonth(), attendanceDate.getDate()),
                lt: new Date(attendanceDate.getFullYear(), attendanceDate.getMonth(), attendanceDate.getDate() + 1)
              }
            }
          })

          let attendanceRecord

          if (existing && bulkData.overwriteExisting) {
            // Update existing record
            attendanceRecord = await tx.attendance.update({
              where: { id: existing.id },
              data: {
                status: update.status,
                timeIn: update.timeIn,
                timeOut: update.timeOut,
                remarks: update.remarks ? 
                  `${existing.remarks || ''} | Bulk Update: ${update.remarks}`.trim() :
                  existing.remarks
              },
              include: {
                student: {
                  select: {
                    id: true,
                    studentNumber: true,
                    firstName: true,
                    lastName: true,
                    middleName: true,
                    gradeLevel: true,
                    section: true
                  }
                }
              }
            })
          } else if (!existing) {
            // Create new record
            attendanceRecord = await tx.attendance.create({
              data: {
                studentId: update.studentId,
                teacherId: bulkData.teacherId,
                date: attendanceDate,
                timeIn: update.timeIn,
                timeOut: update.timeOut,
                status: update.status,
                remarks: update.remarks ? `Bulk Entry: ${update.remarks}` : 'Bulk Entry'
              },
              include: {
                student: {
                  select: {
                    id: true,
                    studentNumber: true,
                    firstName: true,
                    lastName: true,
                    middleName: true,
                    gradeLevel: true,
                    section: true
                  }
                }
              }
            })
          } else {
            // Record exists and not overwriting
            errors.push({
              index: i,
              studentId: update.studentId,
              error: 'Attendance record already exists'
            })
            continue
          }

          processedUpdates.push({
            index: i,
            studentId: update.studentId,
            attendanceId: attendanceRecord.id,
            action: existing ? 'updated' : 'created',
            student: attendanceRecord.student
          })

          // Queue SMS notification
          smsNotifications.push({
            studentId: update.studentId,
            attendanceId: attendanceRecord.id,
            status: update.status,
            timeIn: update.timeIn
          })

        } catch (error) {
          console.error(`Bulk update error for student ${update.studentId}:`, error)
          errors.push({
            index: i,
            studentId: update.studentId,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return { processedUpdates, errors, smsNotifications }
    })

    // Send SMS notifications asynchronously
    if (results.smsNotifications.length > 0) {
      smsUtils.sendBulkNotifications(results.smsNotifications)
        .catch(error => {
          console.warn('Bulk SMS notifications failed:', error)
        })
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'CREATE',
      resource: 'attendance',
      details: {
        method: 'bulk_update',
        totalUpdates: bulkData.updates.length,
        successful: results.processedUpdates.length,
        failed: results.errors.length,
        overwriteExisting: bulkData.overwriteExisting,
        teacherId: bulkData.teacherId,
        studentIds: studentIds.slice(0, 10) // Log first 10 student IDs
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          total: bulkData.updates.length,
          successful: results.processedUpdates.length,
          failed: results.errors.length,
          created: results.processedUpdates.filter(u => u.action === 'created').length,
          updated: results.processedUpdates.filter(u => u.action === 'updated').length
        },
        processedUpdates: results.processedUpdates,
        errors: results.errors,
        smsNotificationsSent: results.smsNotifications.length
      },
      message: `Bulk update completed: ${results.processedUpdates.length} successful, ${results.errors.length} failed`
    }, { status: 201 })

  } catch (error) {
    console.error('POST /api/attendance/bulk-update error:', error)
    
    // Log error for debugging
    try {
      const token = request.cookies.get('access-token')?.value ||
                    request.headers.get('authorization')?.replace('Bearer ', '')
      const user = token ? await getUserFromToken(token) : null
      
      await auditHelpers.log({
        userId: user?.id,
        action: 'CREATE',
        resource: 'attendance',
        details: {
          method: 'bulk_update',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        }
      }, request)
    } catch (auditError) {
      console.error('Audit logging failed:', auditError)
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to process bulk attendance update'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to perform bulk attendance updates.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to perform bulk attendance updates.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to perform bulk attendance updates.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to perform bulk attendance updates.' },
    { status: 405 }
  )
}
