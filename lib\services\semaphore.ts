import { 
  SemaphoreAPIResponse, 
  SemaphoreErrorResponse, 
  SemaphoreBalanceResponse,
  SMSResponse,
  SMSDeliveryStatus,
  SMSError
} from '@/lib/types/sms'

export class SemaphoreService {
  private apiKey: string
  private baseUrl: string
  private senderName: string

  constructor(apiKey?: string, senderName?: string) {
    this.apiKey = apiKey || process.env.SEMAPHORE_API_KEY || ''
    this.baseUrl = process.env.SEMAPHORE_BASE_URL || 'https://api.semaphore.co'
    this.senderName = senderName || process.env.SEMAPHORE_SENDER_NAME || 'TSAT'

    if (!this.apiKey) {
      throw new Error('Semaphore API key is required')
    }
  }

  /**
   * Send SMS via Semaphore API
   */
  async sendSMS(
    recipientNumber: string, 
    message: string, 
    options?: {
      senderName?: string
      priority?: boolean
    }
  ): Promise<SMSResponse> {
    try {
      // Format phone number for Philippines
      const formattedNumber = this.formatPhoneNumber(recipientNumber)
      
      if (!formattedNumber) {
        return {
          success: false,
          error: 'Invalid phone number format'
        }
      }

      const payload = {
        apikey: this.apiKey,
        number: formattedNumber,
        message: message.substring(0, 1600), // Semaphore limit
        sendername: options?.senderName || this.senderName
      }

      const response = await fetch(`${this.baseUrl}/api/v4/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(payload)
      })

      const data = await response.json()

      if (!response.ok) {
        const errorData = data as SemaphoreErrorResponse
        return {
          success: false,
          error: errorData.message || errorData.error || 'SMS sending failed',
          details: errorData
        }
      }

      const semaphoreResponse = data as SemaphoreAPIResponse[]
      const firstResponse = semaphoreResponse[0]

      if (!firstResponse || !firstResponse.message_id) {
        return {
          success: false,
          error: 'Invalid response from Semaphore API',
          details: data
        }
      }

      // Calculate cost (approximate - Semaphore charges per message)
      const cost = this.calculateCost(message)

      return {
        success: true,
        messageId: firstResponse.message_id.toString(),
        cost,
        details: firstResponse
      }

    } catch (error) {
      console.error('Semaphore SMS Error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(
    messages: Array<{
      recipientNumber: string
      message: string
    }>,
    options?: {
      senderName?: string
      batchSize?: number
    }
  ): Promise<Array<SMSResponse>> {
    const batchSize = options?.batchSize || 100 // Semaphore batch limit
    const results: SMSResponse[] = []

    // Process in batches
    for (let i = 0; i < messages.length; i += batchSize) {
      const batch = messages.slice(i, i + batchSize)
      
      // Process batch concurrently with rate limiting
      const batchPromises = batch.map(async (msg, index) => {
        // Add small delay to prevent rate limiting
        await this.delay(index * 100)
        return this.sendSMS(msg.recipientNumber, msg.message, options)
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Add delay between batches
      if (i + batchSize < messages.length) {
        await this.delay(1000)
      }
    }

    return results
  }

  /**
   * Check SMS delivery status
   */
  async checkDeliveryStatus(messageId: string): Promise<SMSDeliveryStatus> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v4/messages/${messageId}?apikey=${this.apiKey}`
      )

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json() as SemaphoreAPIResponse

      let status: 'delivered' | 'failed' | 'pending' = 'pending'
      let deliveredAt: Date | undefined
      let failureReason: string | undefined

      // Map Semaphore status to our status
      switch (data.status?.toLowerCase()) {
        case 'delivered':
          status = 'delivered'
          deliveredAt = data.updated_at ? new Date(data.updated_at) : undefined
          break
        case 'failed':
        case 'expired':
        case 'rejected':
          status = 'failed'
          failureReason = data.status
          break
        default:
          status = 'pending'
      }

      return {
        messageId,
        status,
        deliveredAt,
        failureReason
      }

    } catch (error) {
      console.error('Semaphore status check error:', error)
      return {
        messageId,
        status: 'failed',
        failureReason: error instanceof Error ? error.message : 'Status check failed'
      }
    }
  }

  /**
   * Get account balance
   */
  async getBalance(): Promise<{ balance: number; currency: string } | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/v4/account?apikey=${this.apiKey}`
      )

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json() as SemaphoreBalanceResponse

      return {
        balance: data.balance || 0,
        currency: data.currency || 'PHP'
      }

    } catch (error) {
      console.error('Semaphore balance check error:', error)
      return null
    }
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const balance = await this.getBalance()
      return {
        success: balance !== null,
        error: balance === null ? 'Failed to connect to Semaphore API' : undefined
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      }
    }
  }

  /**
   * Format Philippine phone number
   */
  private formatPhoneNumber(phoneNumber: string): string | null {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '')

    // Philippine number patterns
    if (cleaned.match(/^63\d{10}$/)) {
      // Already in +63 format
      return cleaned
    } else if (cleaned.match(/^09\d{9}$/)) {
      // Convert 09XXXXXXXXX to 639XXXXXXXXX
      return '63' + cleaned.substring(1)
    } else if (cleaned.match(/^9\d{9}$/)) {
      // Convert 9XXXXXXXXX to 639XXXXXXXXX
      return '63' + cleaned
    }

    return null
  }

  /**
   * Calculate SMS cost (approximate)
   */
  private calculateCost(message: string): number {
    const baseRate = parseFloat(process.env.SMS_COST_PER_MESSAGE || '2.50') // PHP
    const messageLength = message.length
    
    // SMS is charged per 160 characters
    const messageCount = Math.ceil(messageLength / 160)
    
    return baseRate * messageCount
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Validate phone number
   */
  static validatePhoneNumber(phoneNumber: string): boolean {
    const cleaned = phoneNumber.replace(/\D/g, '')
    return !!(
      cleaned.match(/^63\d{10}$/) ||
      cleaned.match(/^09\d{9}$/) ||
      cleaned.match(/^9\d{9}$/)
    )
  }

  /**
   * Get SMS character count and message parts
   */
  static getMessageInfo(message: string): {
    length: number
    parts: number
    maxLength: number
    remaining: number
  } {
    const length = message.length
    const maxSingleLength = 160
    const maxConcatLength = 153 // For concatenated messages
    
    let parts: number
    let maxLength: number
    let remaining: number

    if (length <= maxSingleLength) {
      parts = 1
      maxLength = maxSingleLength
      remaining = maxSingleLength - length
    } else {
      parts = Math.ceil(length / maxConcatLength)
      maxLength = parts * maxConcatLength
      remaining = maxLength - length
    }

    return { length, parts, maxLength, remaining }
  }
}
