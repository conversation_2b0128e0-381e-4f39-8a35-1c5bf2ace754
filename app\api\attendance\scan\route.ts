import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  qrScanSchema,
  type QRScanInput
} from '@/lib/validations/attendance'
import {
  qrCodeUtils,
  attendanceDbUtils,
  attendanceStatusUtils,
  transformUtils
} from '@/lib/utils/attendance'
import { smsUtils } from '@/lib/utils/sms'

/**
 * POST /api/attendance/scan
 * Process QR code scan for attendance recording
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting - more restrictive for scanning to prevent abuse
    const rateLimitResult = await rateLimit(request, 'attendance-scan', 30, 60 * 1000) // 30 scans per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many scan requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (ADMIN, TEACHER, and STAFF can scan)
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = qrScanSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid scan data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const scanData: QRScanInput = validationResult.data

    // Validate QR code and get student information
    const qrValidation = await qrCodeUtils.validateQRCode(scanData.qrData)
    
    if (!qrValidation.valid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid QR code',
          reason: qrValidation.reason
        },
        { status: 400 }
      )
    }

    const studentId = qrValidation.studentId!
    const student = qrValidation.student!

    // Check for duplicate scan (already scanned today)
    const scanDate = scanData.timestamp || new Date()
    const duplicateScan = await qrCodeUtils.checkDuplicateScan(studentId, scanDate)
    
    if (duplicateScan) {
      return NextResponse.json(
        {
          success: false,
          error: 'Student already scanned today',
          student: {
            id: student.id,
            name: `${student.firstName} ${student.lastName}`,
            studentNumber: student.studentNumber,
            gradeLevel: student.gradeLevel,
            section: student.section
          }
        },
        { status: 409 }
      )
    }

    // Get teacher information for attendance record
    let teacherId = user.teacherProfileId
    if (!teacherId && user.role === 'TEACHER') {
      // Try to find teacher profile by user email
      const teacher = await prisma.teacher.findUnique({
        where: { email: user.email },
        select: { id: true }
      })
      teacherId = teacher?.id
    }

    // If no teacher profile found, use a default system teacher or create one
    if (!teacherId) {
      const systemTeacher = await prisma.teacher.findFirst({
        where: { employeeNumber: 'SYSTEM' }
      })
      
      if (!systemTeacher) {
        const newSystemTeacher = await prisma.teacher.create({
          data: {
            employeeNumber: 'SYSTEM',
            firstName: 'System',
            lastName: 'Scanner',
            contactNumber: '00000000000',
            email: '<EMAIL>',
            subjectsHandled: JSON.stringify(['Attendance'])
          }
        })
        teacherId = newSystemTeacher.id
      } else {
        teacherId = systemTeacher.id
      }
    }

    // Calculate attendance status based on time
    const timeIn = scanDate
    const status = attendanceStatusUtils.calculateStatus(timeIn)

    // Create attendance record in transaction
    const attendance = await prisma.$transaction(async (tx) => {
      // Create the attendance record
      const newAttendance = await tx.attendance.create({
        data: {
          studentId,
          teacherId: teacherId!,
          date: scanDate,
          timeIn,
          status,
          remarks: scanData.notes ? 
            `QR Scan - ${scanData.location || 'Unknown location'}: ${scanData.notes}` :
            `QR Scan - ${scanData.location || 'Unknown location'}`
        },
        include: {
          student: {
            select: {
              id: true,
              studentNumber: true,
              firstName: true,
              lastName: true,
              middleName: true,
              gradeLevel: true,
              section: true,
              status: true
            }
          },
          teacher: {
            select: {
              id: true,
              employeeNumber: true,
              firstName: true,
              lastName: true,
              middleName: true
            }
          }
        }
      })

      return newAttendance
    })

    // Send SMS notification asynchronously (don't wait for it)
    smsUtils.sendAttendanceNotification(
      studentId,
      attendance.id,
      status,
      timeIn
    ).catch(error => {
      console.warn('SMS notification failed:', error)
      // Log but don't fail the request
    })

    // Transform response data
    const transformedAttendance = transformUtils.transformAttendanceForResponse(attendance)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'CREATE',
      resource: 'attendance',
      resourceId: attendance.id,
      details: {
        method: 'qr_scan',
        studentId,
        studentName: `${student.firstName} ${student.lastName}`,
        studentNumber: student.studentNumber,
        status,
        timeIn: timeIn.toISOString(),
        location: scanData.location,
        qrData: scanData.qrData.substring(0, 20) + '...' // Log partial QR data for security
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedAttendance,
      message: `Attendance recorded successfully - ${attendanceStatusUtils.getStatusDisplay(status)}`,
      scan: {
        timestamp: timeIn,
        location: scanData.location,
        status: attendanceStatusUtils.getStatusDisplay(status),
        statusColor: attendanceStatusUtils.getStatusColor(status)
      }
    }, { status: 201 })

  } catch (error) {
    console.error('POST /api/attendance/scan error:', error)
    
    // Log error for debugging
    try {
      const token = request.cookies.get('access-token')?.value ||
                    request.headers.get('authorization')?.replace('Bearer ', '')
      const user = token ? await getUserFromToken(token) : null
      
      await auditHelpers.log({
        userId: user?.id,
        action: 'CREATE',
        resource: 'attendance',
        details: {
          method: 'qr_scan',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        }
      }, request)
    } catch (auditError) {
      console.error('Audit logging failed:', auditError)
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to process QR code scan'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to scan QR codes.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to scan QR codes.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to scan QR codes.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to scan QR codes.' },
    { status: 405 }
  )
}
