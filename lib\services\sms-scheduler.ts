import { prisma } from '@/lib/db'
import { SMSQueueService } from './sms-queue'
import { SMSTemplateService } from './sms-template'
import { SMSBlacklistService } from './sms-blacklist'
import { 
  SendSMSInput,
  SendBatchSMSInput,
  SendTemplatedSMSInput 
} from '@/lib/types/sms'
import { SMSStatus, SMSType, SMSPriority } from '../../generated/prisma'

export interface ScheduledSMSJob {
  id: string
  name: string
  description?: string
  cronExpression?: string
  scheduledFor?: Date
  isRecurring: boolean
  isActive: boolean
  lastRun?: Date
  nextRun?: Date
  runCount: number
  maxRuns?: number
  jobType: 'single' | 'batch' | 'template'
  jobData: any
  createdBy?: string
  createdAt: Date
  updatedAt: Date
}

export class SMSSchedulerService {
  private smsQueue: SMSQueueService
  private schedulerInterval: NodeJS.Timeout | null = null
  private isRunning = false

  constructor() {
    this.smsQueue = new SMSQueueService()
  }

  /**
   * Schedule a single SMS
   */
  async scheduleSingleSMS(
    input: SendSMSInput & {
      name: string
      description?: string
      scheduledFor: Date
    },
    createdBy?: string
  ): Promise<string> {
    // Validate scheduled time is in the future
    if (input.scheduledFor <= new Date()) {
      throw new Error('Scheduled time must be in the future')
    }

    // Create scheduled job record
    const job = await prisma.sMSLog.create({
      data: {
        recipientNumber: input.recipientNumber,
        message: input.message,
        type: input.type || SMSType.CUSTOM,
        priority: input.priority || SMSPriority.NORMAL,
        status: SMSStatus.QUEUED,
        scheduledFor: input.scheduledFor,
        studentId: input.studentId,
        attendanceId: input.attendanceId,
        templateId: input.templateId,
        userId: createdBy
      }
    })

    return job.id
  }

  /**
   * Schedule batch SMS
   */
  async scheduleBatchSMS(
    input: SendBatchSMSInput & {
      name: string
      description?: string
      scheduledFor: Date
    },
    createdBy?: string
  ): Promise<string> {
    // Validate scheduled time is in the future
    if (input.scheduledFor <= new Date()) {
      throw new Error('Scheduled time must be in the future')
    }

    const batchResult = await this.smsQueue.addBatchToQueue(input, createdBy)
    return batchResult.batchId || 'batch_scheduled'
  }

  /**
   * Schedule recurring SMS using cron expression
   */
  async scheduleRecurringSMS(
    input: {
      name: string
      description?: string
      cronExpression: string
      jobType: 'single' | 'batch' | 'template'
      jobData: any
      maxRuns?: number
      isActive?: boolean
    },
    createdBy?: string
  ): Promise<string> {
    // Validate cron expression
    if (!this.isValidCronExpression(input.cronExpression)) {
      throw new Error('Invalid cron expression')
    }

    // Calculate next run time
    const nextRun = this.getNextRunTime(input.cronExpression)
    if (!nextRun) {
      throw new Error('Unable to calculate next run time from cron expression')
    }

    // Create job record (we'll use a separate table for this in a real implementation)
    // For now, we'll store it as a special SMS log entry
    const jobId = `recurring_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Store job configuration in a JSON field or separate table
    // This is a simplified implementation
    const job = await prisma.sMSLog.create({
      data: {
        recipientNumber: 'RECURRING_JOB',
        message: JSON.stringify({
          jobId,
          name: input.name,
          description: input.description,
          cronExpression: input.cronExpression,
          jobType: input.jobType,
          jobData: input.jobData,
          maxRuns: input.maxRuns,
          isActive: input.isActive !== false,
          runCount: 0,
          nextRun: nextRun.toISOString()
        }),
        type: SMSType.CUSTOM,
        priority: SMSPriority.NORMAL,
        status: SMSStatus.QUEUED,
        scheduledFor: nextRun,
        userId: createdBy
      }
    })

    return jobId
  }

  /**
   * Cancel scheduled SMS
   */
  async cancelScheduledSMS(smsLogId: string): Promise<void> {
    const smsLog = await prisma.sMSLog.findUnique({
      where: { id: smsLogId }
    })

    if (!smsLog) {
      throw new Error('Scheduled SMS not found')
    }

    if (smsLog.status !== SMSStatus.QUEUED) {
      throw new Error('Cannot cancel SMS that is not queued')
    }

    await prisma.sMSLog.update({
      where: { id: smsLogId },
      data: {
        status: SMSStatus.CANCELLED,
        failedAt: new Date(),
        errorMessage: 'Cancelled by user'
      }
    })
  }

  /**
   * Get scheduled SMS jobs
   */
  async getScheduledJobs(
    filters?: {
      isActive?: boolean
      jobType?: string
      createdBy?: string
      startDate?: Date
      endDate?: Date
    }
  ): Promise<Array<{
    id: string
    recipientNumber: string
    message: string
    scheduledFor: Date
    status: SMSStatus
    type: SMSType
    priority: SMSPriority
    createdAt: Date
    isRecurring: boolean
    jobData?: any
  }>> {
    const where: any = {
      status: SMSStatus.QUEUED,
      scheduledFor: { not: null }
    }

    if (filters?.startDate || filters?.endDate) {
      where.scheduledFor = {}
      if (filters.startDate) {
        where.scheduledFor.gte = filters.startDate
      }
      if (filters.endDate) {
        where.scheduledFor.lte = filters.endDate
      }
    }

    if (filters?.createdBy) {
      where.userId = filters.createdBy
    }

    const jobs = await prisma.sMSLog.findMany({
      where,
      orderBy: { scheduledFor: 'asc' }
    })

    return jobs.map(job => {
      let jobData = null
      let isRecurring = false

      // Check if this is a recurring job
      if (job.recipientNumber === 'RECURRING_JOB') {
        try {
          jobData = JSON.parse(job.message)
          isRecurring = true
        } catch (error) {
          // Not a valid recurring job
        }
      }

      return {
        id: job.id,
        recipientNumber: job.recipientNumber,
        message: job.message,
        scheduledFor: job.scheduledFor!,
        status: job.status,
        type: job.type,
        priority: job.priority,
        createdAt: job.createdAt,
        isRecurring,
        jobData
      }
    })
  }

  /**
   * Process scheduled jobs
   */
  async processScheduledJobs(): Promise<void> {
    const now = new Date()

    // Get jobs that are due to run
    const dueJobs = await prisma.sMSLog.findMany({
      where: {
        status: SMSStatus.QUEUED,
        scheduledFor: {
          lte: now
        }
      },
      orderBy: { priority: 'desc' }
    })

    for (const job of dueJobs) {
      try {
        if (job.recipientNumber === 'RECURRING_JOB') {
          // Handle recurring job
          await this.processRecurringJob(job)
        } else {
          // Handle regular scheduled SMS
          await this.processScheduledSMS(job)
        }
      } catch (error) {
        console.error(`Error processing scheduled job ${job.id}:`, error)
        
        // Mark job as failed
        await prisma.sMSLog.update({
          where: { id: job.id },
          data: {
            status: SMSStatus.FAILED,
            failedAt: new Date(),
            errorMessage: error instanceof Error ? error.message : 'Processing error'
          }
        })
      }
    }
  }

  /**
   * Process a regular scheduled SMS
   */
  private async processScheduledSMS(job: any): Promise<void> {
    // Check if recipient is blacklisted
    const isBlacklisted = await SMSBlacklistService.isBlacklisted(job.recipientNumber)
    if (isBlacklisted) {
      await prisma.sMSLog.update({
        where: { id: job.id },
        data: {
          status: SMSStatus.FAILED,
          failedAt: new Date(),
          errorMessage: 'Recipient is blacklisted'
        }
      })
      return
    }

    // Update status to pending and let the queue processor handle it
    await prisma.sMSLog.update({
      where: { id: job.id },
      data: {
        status: SMSStatus.PENDING,
        scheduledFor: null // Remove scheduled time since it's now being processed
      }
    })
  }

  /**
   * Process a recurring job
   */
  private async processRecurringJob(job: any): Promise<void> {
    try {
      const jobData = JSON.parse(job.message)
      
      if (!jobData.isActive) {
        // Job is inactive, skip
        return
      }

      // Check if max runs reached
      if (jobData.maxRuns && jobData.runCount >= jobData.maxRuns) {
        // Mark job as completed
        await prisma.sMSLog.update({
          where: { id: job.id },
          data: {
            status: SMSStatus.DELIVERED, // Use DELIVERED to indicate completion
            deliveredAt: new Date()
          }
        })
        return
      }

      // Execute the job based on type
      switch (jobData.jobType) {
        case 'single':
          await this.smsQueue.addToQueue(jobData.jobData, job.userId)
          break
        case 'batch':
          await this.smsQueue.addBatchToQueue(jobData.jobData, job.userId)
          break
        case 'template':
          await this.smsQueue.addTemplatedToQueue(jobData.jobData, job.userId)
          break
      }

      // Calculate next run time
      const nextRun = this.getNextRunTime(jobData.cronExpression)
      
      if (nextRun) {
        // Update job for next run
        jobData.runCount++
        jobData.lastRun = new Date().toISOString()
        jobData.nextRun = nextRun.toISOString()

        await prisma.sMSLog.update({
          where: { id: job.id },
          data: {
            message: JSON.stringify(jobData),
            scheduledFor: nextRun
          }
        })
      } else {
        // No more runs, mark as completed
        await prisma.sMSLog.update({
          where: { id: job.id },
          data: {
            status: SMSStatus.DELIVERED,
            deliveredAt: new Date()
          }
        })
      }

    } catch (error) {
      console.error('Error processing recurring job:', error)
      throw error
    }
  }

  /**
   * Start the scheduler
   */
  startScheduler(intervalMs = 60000): void { // Check every minute
    if (this.isRunning) {
      return
    }

    this.isRunning = true
    this.schedulerInterval = setInterval(async () => {
      await this.processScheduledJobs()
    }, intervalMs)

    console.log('SMS scheduler started')
  }

  /**
   * Stop the scheduler
   */
  stopScheduler(): void {
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval)
      this.schedulerInterval = null
    }
    this.isRunning = false
    console.log('SMS scheduler stopped')
  }

  /**
   * Validate cron expression (simplified validation)
   */
  private isValidCronExpression(cronExpression: string): boolean {
    // Basic validation for cron expression
    // Format: minute hour day month dayOfWeek
    const parts = cronExpression.trim().split(/\s+/)
    
    if (parts.length !== 5) {
      return false
    }

    // More detailed validation would go here
    // For now, just check basic format
    return true
  }

  /**
   * Get next run time from cron expression (simplified implementation)
   */
  private getNextRunTime(cronExpression: string): Date | null {
    // This is a simplified implementation
    // In a real application, you would use a proper cron parser library
    
    const now = new Date()
    const parts = cronExpression.trim().split(/\s+/)
    
    if (parts.length !== 5) {
      return null
    }

    // For demonstration, handle some basic patterns
    if (cronExpression === '0 9 * * *') {
      // Daily at 9 AM
      const next = new Date(now)
      next.setHours(9, 0, 0, 0)
      if (next <= now) {
        next.setDate(next.getDate() + 1)
      }
      return next
    }

    if (cronExpression === '0 9 * * 1') {
      // Weekly on Monday at 9 AM
      const next = new Date(now)
      next.setHours(9, 0, 0, 0)
      const daysUntilMonday = (1 - next.getDay() + 7) % 7
      if (daysUntilMonday === 0 && next <= now) {
        next.setDate(next.getDate() + 7)
      } else {
        next.setDate(next.getDate() + daysUntilMonday)
      }
      return next
    }

    // Default: add 1 hour for testing
    return new Date(now.getTime() + 60 * 60 * 1000)
  }

  /**
   * Get scheduler statistics
   */
  async getSchedulerStats(): Promise<{
    totalScheduled: number
    dueNow: number
    dueToday: number
    recurringJobs: number
    completedJobs: number
  }> {
    const now = new Date()
    const endOfDay = new Date(now)
    endOfDay.setHours(23, 59, 59, 999)

    const [totalScheduled, dueNow, dueToday, recurringJobs, completedJobs] = await Promise.all([
      prisma.sMSLog.count({
        where: {
          status: SMSStatus.QUEUED,
          scheduledFor: { not: null }
        }
      }),
      prisma.sMSLog.count({
        where: {
          status: SMSStatus.QUEUED,
          scheduledFor: { lte: now }
        }
      }),
      prisma.sMSLog.count({
        where: {
          status: SMSStatus.QUEUED,
          scheduledFor: {
            gte: now,
            lte: endOfDay
          }
        }
      }),
      prisma.sMSLog.count({
        where: {
          recipientNumber: 'RECURRING_JOB',
          status: SMSStatus.QUEUED
        }
      }),
      prisma.sMSLog.count({
        where: {
          scheduledFor: { not: null },
          status: { in: [SMSStatus.SENT, SMSStatus.DELIVERED] }
        }
      })
    ])

    return {
      totalScheduled,
      dueNow,
      dueToday,
      recurringJobs,
      completedJobs
    }
  }
}
