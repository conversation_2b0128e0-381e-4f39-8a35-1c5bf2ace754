import { describe, it, expect, beforeAll, afterAll, jest } from '@jest/globals'
import { SMSService } from '@/lib/services/sms-service'
import { SemaphoreService } from '@/lib/services/semaphore'
import { SMSTemplateService } from '@/lib/services/sms-template'
import { SMSBlacklistService } from '@/lib/services/sms-blacklist'
import { SMSType, SMSPriority, TemplateCategory } from '../generated/prisma'

// Mock environment variables
process.env.SEMAPHORE_API_KEY = 'test-api-key'
process.env.SEMAPHORE_BASE_URL = 'https://api.semaphore.co'
process.env.SEMAPHORE_SENDER_NAME = 'TEST'
process.env.SMS_COST_PER_MESSAGE = '2.50'

// Mock Semaphore API responses
jest.mock('@/lib/services/semaphore')
jest.mock('@/lib/db', () => ({
  prisma: {
    sMSLog: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn()
    },
    sMSTemplate: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      delete: jest.fn()
    },
    sMSBlacklist: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      delete: jest.fn(),
      count: jest.fn()
    },
    sMSCost: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      upsert: jest.fn()
    },
    sMSSetting: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      upsert: jest.fn()
    }
  }
}))

describe('SMS Service', () => {
  let smsService: SMSService

  beforeAll(async () => {
    smsService = SMSService.getInstance()
  })

  afterAll(() => {
    smsService.shutdown()
  })

  describe('Phone Number Validation', () => {
    it('should validate Philippine phone numbers correctly', () => {
      expect(SemaphoreService.validatePhoneNumber('09123456789')).toBe(true)
      expect(SemaphoreService.validatePhoneNumber('639123456789')).toBe(true)
      expect(SemaphoreService.validatePhoneNumber('9123456789')).toBe(true)
      expect(SemaphoreService.validatePhoneNumber('1234567890')).toBe(false)
      expect(SemaphoreService.validatePhoneNumber('invalid')).toBe(false)
    })

    it('should format phone numbers correctly', () => {
      const validation1 = SMSBlacklistService.validatePhoneNumber('09123456789')
      expect(validation1.isValid).toBe(true)
      expect(validation1.formatted).toBe('639123456789')

      const validation2 = SMSBlacklistService.validatePhoneNumber('639123456789')
      expect(validation2.isValid).toBe(true)
      expect(validation2.formatted).toBe('639123456789')
    })
  })

  describe('Message Info Calculation', () => {
    it('should calculate message parts correctly', () => {
      const shortMessage = 'Hello World'
      const info1 = SemaphoreService.getMessageInfo(shortMessage)
      expect(info1.length).toBe(11)
      expect(info1.parts).toBe(1)
      expect(info1.remaining).toBe(149)

      const longMessage = 'A'.repeat(200)
      const info2 = SemaphoreService.getMessageInfo(longMessage)
      expect(info2.length).toBe(200)
      expect(info2.parts).toBe(2)
    })
  })

  describe('Template System', () => {
    it('should render templates with variables correctly', () => {
      const template = 'Hello {name}, your child {studentName} has arrived at {time}.'
      const variables = {
        name: 'Mrs. Santos',
        studentName: 'John',
        time: '7:30 AM'
      }

      const rendered = SMSTemplateService.renderTemplate(template, variables)
      expect(rendered).toBe('Hello Mrs. Santos, your child John has arrived at 7:30 AM.')
    })

    it('should handle missing variables gracefully', () => {
      const template = 'Hello {name}, your child {studentName} has arrived.'
      const variables = { name: 'Mrs. Santos' }

      const rendered = SMSTemplateService.renderTemplate(template, variables)
      expect(rendered).toBe('Hello Mrs. Santos, your child has arrived.')
    })

    it('should validate templates correctly', () => {
      const content = 'Hello {name}, your child {studentName} has arrived.'
      const variables = ['name', 'studentName']

      const validation = SMSTemplateService.validateTemplate(content, variables)
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toHaveLength(0)
    })

    it('should detect template validation errors', () => {
      const content = 'Hello {name}, your child {undeclaredVar} has arrived.'
      const variables = ['name']

      const validation = SMSTemplateService.validateTemplate(content, variables)
      expect(validation.isValid).toBe(true) // Still valid, but with warnings
      expect(validation.warnings.length).toBeGreaterThan(0)
    })
  })

  describe('Default Templates', () => {
    it('should provide default system templates', () => {
      const defaultTemplates = SMSTemplateService.getDefaultTemplates()
      expect(defaultTemplates.length).toBeGreaterThan(0)

      const attendanceTemplate = defaultTemplates.find(t => t.name === 'Student Arrival')
      expect(attendanceTemplate).toBeDefined()
      expect(attendanceTemplate?.category).toBe(TemplateCategory.ATTENDANCE)
      expect(attendanceTemplate?.isSystem).toBe(true)
    })

    it('should provide available template variables', () => {
      const variables = SMSTemplateService.getAvailableVariables()
      expect(variables.student).toBeDefined()
      expect(variables.attendance).toBeDefined()
      expect(variables.school).toBeDefined()
      expect(variables.general).toBeDefined()

      expect(variables.student.length).toBeGreaterThan(0)
      expect(variables.student[0]).toHaveProperty('name')
      expect(variables.student[0]).toHaveProperty('description')
      expect(variables.student[0]).toHaveProperty('example')
    })
  })

  describe('Blacklist Management', () => {
    it('should validate phone numbers for blacklist', () => {
      const validation1 = SMSBlacklistService.validatePhoneNumber('09123456789')
      expect(validation1.isValid).toBe(true)
      expect(validation1.type).toBe('mobile')
      expect(validation1.country).toBe('Philippines')

      const validation2 = SMSBlacklistService.validatePhoneNumber('invalid')
      expect(validation2.isValid).toBe(false)
    })
  })

  describe('Cost Calculation', () => {
    it('should calculate SMS costs correctly', () => {
      // Mock the private method by testing through SemaphoreService
      const semaphoreService = new SemaphoreService()
      
      // Test cost calculation indirectly through message info
      const shortMessage = 'Hello'
      const longMessage = 'A'.repeat(200)
      
      const info1 = SemaphoreService.getMessageInfo(shortMessage)
      const info2 = SemaphoreService.getMessageInfo(longMessage)
      
      expect(info1.parts).toBe(1)
      expect(info2.parts).toBe(2)
      
      // Cost should be proportional to parts
      const baseCost = 2.50
      expect(info1.parts * baseCost).toBe(2.50)
      expect(info2.parts * baseCost).toBe(5.00)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid phone numbers gracefully', async () => {
      const result = await smsService.sendSMS({
        recipientNumber: 'invalid-number',
        message: 'Test message',
        type: SMSType.CUSTOM,
        priority: SMSPriority.NORMAL
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid')
    })

    it('should handle empty messages gracefully', async () => {
      const result = await smsService.sendSMS({
        recipientNumber: '09123456789',
        message: '',
        type: SMSType.CUSTOM,
        priority: SMSPriority.NORMAL
      })

      expect(result.success).toBe(false)
    })
  })

  describe('System Health', () => {
    it('should perform health checks', async () => {
      const health = await smsService.healthCheck()
      expect(health).toHaveProperty('status')
      expect(health).toHaveProperty('checks')
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status)
    })

    it('should get system status', async () => {
      const status = await smsService.getSystemStatus()
      expect(status).toHaveProperty('semaphore')
      expect(status).toHaveProperty('queue')
      expect(status).toHaveProperty('scheduler')
      expect(status).toHaveProperty('budget')
      expect(status).toHaveProperty('blacklist')
    })

    it('should get system configuration', async () => {
      const config = await smsService.getConfiguration()
      expect(config).toHaveProperty('semaphoreApiKey')
      expect(config).toHaveProperty('semaphoreBaseUrl')
      expect(config).toHaveProperty('defaultSenderName')
      expect(config).toHaveProperty('costPerSMS')
      expect(config).toHaveProperty('dailyBudgetLimit')
    })
  })

  describe('Integration Tests', () => {
    it('should handle complete SMS workflow', async () => {
      // This would be a more comprehensive test in a real environment
      // For now, we'll test the basic workflow structure
      
      const smsInput = {
        recipientNumber: '09123456789',
        message: 'Test message for integration',
        type: SMSType.CUSTOM,
        priority: SMSPriority.NORMAL
      }

      // Test queueing
      const queueResult = await smsService.queueSMS(smsInput, 'test-user')
      expect(queueResult).toHaveProperty('success')
      
      // Test batch processing
      const batchResult = await smsService.sendBatchSMS({
        recipients: [
          { recipientNumber: '09123456789', message: 'Test 1' },
          { recipientNumber: '09987654321', message: 'Test 2' }
        ],
        type: SMSType.CUSTOM,
        priority: SMSPriority.NORMAL
      }, 'test-user')
      
      expect(batchResult).toHaveProperty('success')
      expect(batchResult).toHaveProperty('totalMessages')
      expect(batchResult).toHaveProperty('results')
    })
  })
})

describe('SMS Template Service', () => {
  describe('Template Validation', () => {
    it('should validate template content length', () => {
      const longContent = 'A'.repeat(2000)
      const validation = SMSTemplateService.validateTemplate(longContent, [])
      expect(validation.isValid).toBe(false)
      expect(validation.errors).toContain('Template content exceeds maximum length of 1600 characters')
    })

    it('should validate empty template content', () => {
      const validation = SMSTemplateService.validateTemplate('', [])
      expect(validation.isValid).toBe(false)
      expect(validation.errors).toContain('Template content cannot be empty')
    })
  })
})

describe('SMS Blacklist Service', () => {
  describe('CSV Import/Export', () => {
    it('should parse CSV data correctly', async () => {
      const csvData = `Phone Number,Reason
09123456789,User requested opt-out
09987654321,Invalid number`

      // This would test the actual CSV parsing in a real implementation
      const lines = csvData.split('\n').slice(1) // Skip header
      expect(lines).toHaveLength(2)
      
      const firstLine = lines[0].split(',')
      expect(firstLine[0]).toBe('09123456789')
      expect(firstLine[1]).toBe('User requested opt-out')
    })
  })
})
