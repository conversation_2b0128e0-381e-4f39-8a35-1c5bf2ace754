import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  studentAttendanceHistorySchema,
  studentIdParamSchema,
  type StudentAttendanceHistoryInput,
  type StudentIdParamInput
} from '@/lib/validations/attendance'
import {
  attendanceDbUtils,
  attendanceStatusUtils,
  transformUtils
} from '@/lib/utils/attendance'

/**
 * GET /api/attendance/student/[id]
 * Get attendance history for a specific student
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-student-history', 100, 60 * 1000)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Validate student ID parameter
    const paramValidation = studentIdParamSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid student ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    const { id: studentId }: StudentIdParamInput = paramValidation.data

    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const validationResult = studentAttendanceHistorySchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const query: StudentAttendanceHistoryInput = validationResult.data

    // Verify student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        middleName: true,
        gradeLevel: true,
        section: true,
        status: true,
        dateEnrolled: true
      }
    })

    if (!student) {
      return NextResponse.json(
        {
          success: false,
          error: 'Student not found'
        },
        { status: 404 }
      )
    }

    // Set default date range if not provided
    const endDate = query.endDate || new Date()
    const startDate = query.startDate || new Date(endDate.getTime() - (30 * 24 * 60 * 60 * 1000)) // 30 days ago

    // Build where clause
    const where: any = {
      studentId,
      date: {
        gte: startDate,
        lte: endDate
      }
    }

    if (query.status) {
      where.status = query.status
    }

    // Calculate pagination
    const skip = (query.page - 1) * query.limit

    // Get attendance records and total count
    const [attendanceRecords, totalCount] = await Promise.all([
      prisma.attendance.findMany({
        where,
        include: {
          teacher: {
            select: {
              id: true,
              employeeNumber: true,
              firstName: true,
              lastName: true,
              middleName: true
            }
          },
          smsLogs: {
            select: {
              id: true,
              status: true,
              sentAt: true,
              deliveredAt: true
            }
          }
        },
        orderBy: {
          date: 'desc'
        },
        skip,
        take: query.limit
      }),
      prisma.attendance.count({ where })
    ])

    // Transform attendance records
    const transformedRecords = attendanceRecords.map(record => ({
      ...transformUtils.transformAttendanceForResponse(record),
      smsNotifications: record.smsLogs.map(sms => ({
        id: sms.id,
        status: sms.status,
        sentAt: sms.sentAt,
        deliveredAt: sms.deliveredAt
      }))
    }))

    // Calculate attendance statistics for the period
    const stats = await attendanceDbUtils.getAttendanceStats(startDate, endDate, {
      gradeLevel: student.gradeLevel,
      section: student.section
    })

    // Get student-specific statistics
    const studentStats = await prisma.attendance.groupBy({
      by: ['status'],
      where: {
        studentId,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: {
        status: true
      }
    })

    const studentSummary = {
      total: 0,
      present: 0,
      absent: 0,
      late: 0,
      excused: 0,
      attendanceRate: 0
    }

    studentStats.forEach(stat => {
      studentSummary.total += stat._count.status
      switch (stat.status) {
        case 'PRESENT':
          studentSummary.present = stat._count.status
          break
        case 'ABSENT':
          studentSummary.absent = stat._count.status
          break
        case 'LATE':
          studentSummary.late = stat._count.status
          break
        case 'EXCUSED':
          studentSummary.excused = stat._count.status
          break
      }
    })

    if (studentSummary.total > 0) {
      studentSummary.attendanceRate = 
        ((studentSummary.present + studentSummary.late + studentSummary.excused) / studentSummary.total) * 100
    }

    // Get attendance trends (last 30 days)
    const trendStartDate = new Date(endDate.getTime() - (30 * 24 * 60 * 60 * 1000))
    const trends = await prisma.attendance.findMany({
      where: {
        studentId,
        date: {
          gte: trendStartDate,
          lte: endDate
        }
      },
      select: {
        date: true,
        status: true,
        timeIn: true
      },
      orderBy: {
        date: 'asc'
      }
    })

    const trendData = trends.map(trend => ({
      date: trend.date.toISOString().split('T')[0],
      status: trend.status,
      statusDisplay: attendanceStatusUtils.getStatusDisplay(trend.status),
      statusColor: attendanceStatusUtils.getStatusColor(trend.status),
      timeIn: trend.timeIn
    }))

    // Calculate pagination metadata
    const pagination = transformUtils.calculatePagination(query.page, query.limit, totalCount)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'attendance',
      resourceId: studentId,
      details: {
        endpoint: 'student_history',
        studentId,
        studentName: `${student.firstName} ${student.lastName}`,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        filters: {
          status: query.status
        },
        resultCount: attendanceRecords.length
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        student: {
          id: student.id,
          studentNumber: student.studentNumber,
          firstName: student.firstName,
          lastName: student.lastName,
          middleName: student.middleName,
          fullName: `${student.firstName} ${student.lastName}`,
          gradeLevel: student.gradeLevel,
          section: student.section,
          status: student.status,
          dateEnrolled: student.dateEnrolled
        },
        summary: studentSummary,
        trends: trendData,
        attendanceRecords: transformedRecords
      },
      pagination,
      meta: {
        dateRange: {
          start: startDate.toISOString().split('T')[0],
          end: endDate.toISOString().split('T')[0]
        },
        filters: {
          status: query.status
        },
        classAverage: stats.attendanceRate
      }
    })

  } catch (error) {
    console.error('GET /api/attendance/student/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve student attendance history.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve student attendance history.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve student attendance history.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve student attendance history.' },
    { status: 405 }
  )
}
