import { prisma } from '@/lib/db'
import { PhilippineReportType } from '@/types'
import { ReportType } from '../../generated/prisma'
import { createHash } from 'crypto'
import { reportsConfig } from '@/lib/config/reports'
import { PerformanceMonitorService, monitorPerformance } from './performance-monitor'

export interface CacheEntry {
  key: string
  data: any
  metadata?: any
  expiresAt: Date
}

export interface CacheStats {
  totalEntries: number
  hitRate: number
  totalHits: number
  totalMisses: number
  storageUsed: number
  oldestEntry: Date | null
  newestEntry: Date | null
}

export class ReportCacheService {
  private static instance: ReportCacheService
  private defaultTTL: number = 24 * 60 * 60 * 1000 // 24 hours in milliseconds
  private memoryCache: Map<string, { data: any; expiresAt: Date; hitCount: number }> = new Map()
  private performanceMonitor: PerformanceMonitorService

  private constructor() {
    this.performanceMonitor = PerformanceMonitorService.getInstance()

    // Start cache cleanup interval
    setInterval(() => {
      this.cleanupMemoryCache()
    }, 5 * 60 * 1000) // Every 5 minutes
  }

  static getInstance(): ReportCacheService {
    if (!ReportCacheService.instance) {
      ReportCacheService.instance = new ReportCacheService()
    }
    return ReportCacheService.instance
  }

  /**
   * Get cached data with multi-level caching
   */
  @monitorPerformance('cache_get')
  async get(cacheKey: string): Promise<any | null> {
    try {
      // First check memory cache if enabled
      if (reportsConfig.performance.enableCaching) {
        const memoryEntry = this.memoryCache.get(cacheKey)
        if (memoryEntry && new Date() < memoryEntry.expiresAt) {
          memoryEntry.hitCount++
          await this.recordHit(cacheKey)
          return memoryEntry.data
        }
      }

      // Check database cache
      const cacheEntry = await prisma.reportCache.findUnique({
        where: { cacheKey }
      })

      if (!cacheEntry) {
        await this.recordMiss(cacheKey)
        return null
      }

      // Check if cache entry is expired
      if (new Date() > cacheEntry.expiresAt || !cacheEntry.isValid) {
        await this.invalidate(cacheKey)
        await this.recordMiss(cacheKey)
        return null
      }

      // Update hit count and last accessed
      await prisma.reportCache.update({
        where: { cacheKey },
        data: {
          hitCount: { increment: 1 },
          lastAccessed: new Date()
        }
      })

      const data = JSON.parse(cacheEntry.cachedData)

      // Store in memory cache for faster access
      if (reportsConfig.performance.enableCaching) {
        this.memoryCache.set(cacheKey, {
          data,
          expiresAt: cacheEntry.expiresAt,
          hitCount: 1
        })
      }

      await this.recordHit(cacheKey)
      return data
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  /**
   * Set cached data
   */
  async set(
    cacheKey: string,
    data: any,
    reportType: PhilippineReportType,
    ttl?: number,
    metadata?: any
  ): Promise<void> {
    try {
      const expiresAt = new Date(Date.now() + (ttl || this.defaultTTL))
      const parametersHash = this.generateParametersHash(data)
      const cachedData = JSON.stringify(data)
      const metadataJson = metadata ? JSON.stringify(metadata) : null

      await prisma.reportCache.upsert({
        where: { cacheKey },
        update: {
          cachedData,
          metadata: metadataJson,
          parametersHash,
          expiresAt,
          isValid: true,
          lastAccessed: new Date(),
          updatedAt: new Date()
        },
        create: {
          cacheKey,
          reportType: this.convertToReportType(reportType),
          parametersHash,
          cachedData,
          metadata: metadataJson,
          expiresAt,
          isValid: true,
          hitCount: 0,
          lastAccessed: new Date()
        }
      })
    } catch (error) {
      console.error('Cache set error:', error)
      throw new Error('Failed to cache data')
    }
  }

  /**
   * Invalidate cache entry
   */
  async invalidate(cacheKey: string): Promise<void> {
    try {
      await prisma.reportCache.update({
        where: { cacheKey },
        data: { isValid: false }
      })
    } catch (error) {
      console.error('Cache invalidate error:', error)
    }
  }

  /**
   * Delete cache entry
   */
  async delete(cacheKey: string): Promise<void> {
    try {
      await prisma.reportCache.delete({
        where: { cacheKey }
      })
    } catch (error) {
      console.error('Cache delete error:', error)
    }
  }

  /**
   * Clear all cache entries for a report type
   */
  async clearByReportType(reportType: PhilippineReportType): Promise<number> {
    try {
      const result = await prisma.reportCache.deleteMany({
        where: {
          reportType: this.convertToReportType(reportType)
        }
      })
      return result.count
    } catch (error) {
      console.error('Cache clear by type error:', error)
      return 0
    }
  }

  /**
   * Clear expired cache entries
   */
  async clearExpired(): Promise<number> {
    try {
      const result = await prisma.reportCache.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { isValid: false }
          ]
        }
      })
      return result.count
    } catch (error) {
      console.error('Cache clear expired error:', error)
      return 0
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<CacheStats> {
    try {
      const [totalEntries, oldestEntry, newestEntry] = await Promise.all([
        prisma.reportCache.count(),
        prisma.reportCache.findFirst({
          orderBy: { createdAt: 'asc' },
          select: { createdAt: true }
        }),
        prisma.reportCache.findFirst({
          orderBy: { createdAt: 'desc' },
          select: { createdAt: true }
        })
      ])

      // Calculate hit rate (simplified - in production you'd track this separately)
      const cacheEntries = await prisma.reportCache.findMany({
        select: { hitCount: true, cachedData: true }
      })

      const totalHits = cacheEntries.reduce((sum, entry) => sum + entry.hitCount, 0)
      const totalMisses = Math.max(totalEntries - totalHits, 0) // Simplified calculation
      const hitRate = totalHits + totalMisses > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0

      // Calculate storage used
      const storageUsed = cacheEntries.reduce((sum, entry) => 
        sum + Buffer.byteLength(entry.cachedData, 'utf8'), 0
      )

      return {
        totalEntries,
        hitRate: Math.round(hitRate * 100) / 100,
        totalHits,
        totalMisses,
        storageUsed,
        oldestEntry: oldestEntry?.createdAt || null,
        newestEntry: newestEntry?.createdAt || null
      }
    } catch (error) {
      console.error('Cache stats error:', error)
      return {
        totalEntries: 0,
        hitRate: 0,
        totalHits: 0,
        totalMisses: 0,
        storageUsed: 0,
        oldestEntry: null,
        newestEntry: null
      }
    }
  }

  /**
   * Warm up cache with commonly requested data
   */
  async warmUp(reportType: PhilippineReportType, commonParameters: any[]): Promise<void> {
    try {
      // This would typically pre-generate cache entries for common report parameters
      console.log(`Warming up cache for ${reportType} with ${commonParameters.length} parameter sets`)
      
      // In a real implementation, you would:
      // 1. Generate reports for common parameter combinations
      // 2. Store them in cache
      // 3. This could be done during off-peak hours
      
      for (const params of commonParameters) {
        const cacheKey = this.generateCacheKey(reportType, params)
        // Check if already cached
        const existing = await this.get(cacheKey)
        if (!existing) {
          // Generate and cache the data
          // This would call the report data service
          console.log(`Would generate cache for key: ${cacheKey}`)
        }
      }
    } catch (error) {
      console.error('Cache warm up error:', error)
    }
  }

  /**
   * Get cache entries by report type
   */
  async getEntriesByType(reportType: PhilippineReportType): Promise<any[]> {
    try {
      return await prisma.reportCache.findMany({
        where: {
          reportType: this.convertToReportType(reportType),
          isValid: true,
          expiresAt: { gt: new Date() }
        },
        select: {
          cacheKey: true,
          hitCount: true,
          lastAccessed: true,
          createdAt: true,
          expiresAt: true
        },
        orderBy: { lastAccessed: 'desc' }
      })
    } catch (error) {
      console.error('Get entries by type error:', error)
      return []
    }
  }

  /**
   * Invalidate cache entries by parameters hash
   */
  async invalidateByParametersHash(parametersHash: string): Promise<number> {
    try {
      const result = await prisma.reportCache.updateMany({
        where: { parametersHash },
        data: { isValid: false }
      })
      return result.count
    } catch (error) {
      console.error('Invalidate by parameters hash error:', error)
      return 0
    }
  }

  /**
   * Set cache TTL for different report types
   */
  setTTLForReportType(reportType: PhilippineReportType, ttl: number): void {
    // In a more sophisticated implementation, you'd store this configuration
    // For now, we'll just use the default TTL
    console.log(`Setting TTL for ${reportType} to ${ttl}ms`)
  }

  // Helper methods
  private generateCacheKey(reportType: PhilippineReportType, parameters: any): string {
    const keyData = {
      type: reportType,
      params: parameters
    }
    return createHash('md5').update(JSON.stringify(keyData)).digest('hex')
  }

  private generateParametersHash(data: any): string {
    // Generate a hash of the parameters used to generate this data
    // This helps with cache invalidation when parameters change
    return createHash('md5').update(JSON.stringify(data)).digest('hex').substring(0, 16)
  }

  private convertToReportType(type: PhilippineReportType): ReportType {
    switch (type) {
      case 'SF2': return ReportType.SF2
      case 'SF4': return ReportType.SF4
      case 'custom_attendance': return ReportType.CUSTOM_ATTENDANCE
      case 'sms_notifications': return ReportType.SMS_NOTIFICATIONS
      case 'deped_compliance': return ReportType.DEPED_COMPLIANCE
      default: return ReportType.CUSTOM_ATTENDANCE
    }
  }

  private async recordHit(cacheKey: string): Promise<void> {
    // In a production system, you might want to track hits/misses separately
    // for better analytics
    console.log(`Cache hit for key: ${cacheKey}`)
  }

  private async recordMiss(cacheKey: string): Promise<void> {
    // In a production system, you might want to track hits/misses separately
    // for better analytics
    console.log(`Cache miss for key: ${cacheKey}`)
  }

  /**
   * Optimize cache by removing least recently used entries
   */
  async optimizeCache(maxEntries: number = 1000): Promise<number> {
    try {
      const totalEntries = await prisma.reportCache.count()

      if (totalEntries <= maxEntries) {
        return 0
      }

      // Get least recently used entries
      const entriesToDelete = await prisma.reportCache.findMany({
        orderBy: { lastAccessed: 'asc' },
        take: totalEntries - maxEntries,
        select: { id: true, cacheKey: true }
      })

      const idsToDelete = entriesToDelete.map(entry => entry.id)
      const keysToDelete = entriesToDelete.map(entry => entry.cacheKey)

      // Remove from memory cache
      keysToDelete.forEach(key => this.memoryCache.delete(key))

      const result = await prisma.reportCache.deleteMany({
        where: {
          id: { in: idsToDelete }
        }
      })

      return result.count
    } catch (error) {
      console.error('Cache optimization error:', error)
      return 0
    }
  }

  /**
   * Clean up expired memory cache entries
   */
  private cleanupMemoryCache(): void {
    const now = new Date()
    const keysToDelete: string[] = []

    for (const [key, entry] of this.memoryCache.entries()) {
      if (now > entry.expiresAt) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.memoryCache.delete(key))

    // Limit memory cache size
    const maxMemoryEntries = Math.floor(reportsConfig.performance.cacheSize / 10) // Rough estimate
    if (this.memoryCache.size > maxMemoryEntries) {
      // Remove least recently used entries
      const entries = Array.from(this.memoryCache.entries())
        .sort((a, b) => a[1].hitCount - b[1].hitCount)
        .slice(0, this.memoryCache.size - maxMemoryEntries)

      entries.forEach(([key]) => this.memoryCache.delete(key))
    }
  }

  /**
   * Preload frequently accessed cache entries into memory
   */
  async preloadHotCache(): Promise<void> {
    try {
      if (!reportsConfig.performance.enableCaching) return

      // Get most frequently accessed cache entries
      const hotEntries = await prisma.reportCache.findMany({
        where: {
          isValid: true,
          expiresAt: { gt: new Date() }
        },
        orderBy: { hitCount: 'desc' },
        take: 50 // Top 50 most accessed
      })

      for (const entry of hotEntries) {
        if (!this.memoryCache.has(entry.cacheKey)) {
          this.memoryCache.set(entry.cacheKey, {
            data: JSON.parse(entry.cachedData),
            expiresAt: entry.expiresAt,
            hitCount: entry.hitCount
          })
        }
      }

      console.log(`Preloaded ${hotEntries.length} hot cache entries into memory`)
    } catch (error) {
      console.error('Cache preload error:', error)
    }
  }

  /**
   * Get cache performance metrics
   */
  getCacheMetrics(): {
    memoryCache: {
      size: number
      hitRate: number
      totalHits: number
    }
    databaseCache: {
      size: number
      hitRate: number
    }
  } {
    const memoryEntries = Array.from(this.memoryCache.values())
    const totalMemoryHits = memoryEntries.reduce((sum, entry) => sum + entry.hitCount, 0)

    return {
      memoryCache: {
        size: this.memoryCache.size,
        hitRate: memoryEntries.length > 0 ? totalMemoryHits / memoryEntries.length : 0,
        totalHits: totalMemoryHits
      },
      databaseCache: {
        size: 0, // Would need to query database
        hitRate: 0 // Would need to calculate from stats
      }
    }
  }

  /**
   * Clear all caches
   */
  async clearAllCaches(): Promise<void> {
    try {
      // Clear memory cache
      this.memoryCache.clear()

      // Clear database cache
      await prisma.reportCache.deleteMany({})

      console.log('All caches cleared')
    } catch (error) {
      console.error('Cache clear error:', error)
    }
  }
}
