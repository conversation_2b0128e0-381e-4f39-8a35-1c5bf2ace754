import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  todayAttendanceSchema,
  type TodayAttendanceInput
} from '@/lib/validations/attendance'
import {
  attendanceDbUtils,
  attendanceStatusUtils,
  transformUtils
} from '@/lib/utils/attendance'

/**
 * GET /api/attendance/today
 * Get today's attendance summary with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-today', 100, 60 * 1000) // 100 requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const validationResult = todayAttendanceSchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const query: TodayAttendanceInput = validationResult.data

    // Get today's date range
    const today = new Date()
    const startOfDay = new Date(today)
    startOfDay.setHours(0, 0, 0, 0)
    const endOfDay = new Date(today)
    endOfDay.setHours(23, 59, 59, 999)

    // Build where clause for filtering
    const where: any = {
      date: {
        gte: startOfDay,
        lte: endOfDay
      }
    }

    // Apply grade level and section filters
    if (query.gradeLevel || query.section) {
      where.student = {}
      if (query.gradeLevel) {
        where.student.gradeLevel = query.gradeLevel
      }
      if (query.section) {
        where.student.section = query.section
      }
    }

    // Get attendance statistics for today
    const stats = await attendanceDbUtils.getAttendanceStats(startOfDay, endOfDay, {
      gradeLevel: query.gradeLevel,
      section: query.section
    })

    // Get detailed attendance records if requested
    let attendanceRecords = []
    if (query.includeDetails) {
      const records = await prisma.attendance.findMany({
        where,
        include: {
          student: {
            select: {
              id: true,
              studentNumber: true,
              firstName: true,
              lastName: true,
              middleName: true,
              gradeLevel: true,
              section: true,
              status: true
            }
          },
          teacher: {
            select: {
              id: true,
              employeeNumber: true,
              firstName: true,
              lastName: true,
              middleName: true
            }
          }
        },
        orderBy: [
          { student: { gradeLevel: 'asc' } },
          { student: { section: 'asc' } },
          { student: { lastName: 'asc' } },
          { student: { firstName: 'asc' } }
        ]
      })

      attendanceRecords = records.map(record => 
        transformUtils.transformAttendanceForResponse(record)
      )
    }

    // Get grade/section breakdown
    const gradeBreakdown = await prisma.$queryRaw`
      SELECT 
        s.gradeLevel,
        s.section,
        a.status,
        COUNT(*) as count
      FROM attendances a
      JOIN students s ON a.studentId = s.id
      WHERE a.date >= ${startOfDay} AND a.date <= ${endOfDay}
        ${query.gradeLevel ? `AND s.gradeLevel = ${query.gradeLevel}` : ''}
        ${query.section ? `AND s.section = ${query.section}` : ''}
      GROUP BY s.gradeLevel, s.section, a.status
      ORDER BY s.gradeLevel, s.section
    ` as any[]

    // Process grade breakdown data
    const gradeMap = new Map()
    gradeBreakdown.forEach(item => {
      const key = `${item.gradeLevel}-${item.section}`
      if (!gradeMap.has(key)) {
        gradeMap.set(key, {
          gradeLevel: item.gradeLevel,
          section: item.section,
          present: 0,
          absent: 0,
          late: 0,
          excused: 0,
          total: 0
        })
      }
      
      const sectionData = gradeMap.get(key)
      sectionData.total += Number(item.count)
      
      switch (item.status) {
        case 'PRESENT':
          sectionData.present = Number(item.count)
          break
        case 'ABSENT':
          sectionData.absent = Number(item.count)
          break
        case 'LATE':
          sectionData.late = Number(item.count)
          break
        case 'EXCUSED':
          sectionData.excused = Number(item.count)
          break
      }
    })

    const gradeSectionStats = Array.from(gradeMap.values()).map(section => ({
      ...section,
      attendanceRate: section.total > 0 ? 
        ((section.present + section.late + section.excused) / section.total) * 100 : 0
    }))

    // Get recent activity (last 10 scans)
    const recentActivity = await prisma.attendance.findMany({
      where: {
        date: {
          gte: startOfDay,
          lte: endOfDay
        }
      },
      include: {
        student: {
          select: {
            id: true,
            studentNumber: true,
            firstName: true,
            lastName: true,
            gradeLevel: true,
            section: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    })

    const recentScans = recentActivity.map(record => ({
      id: record.id,
      studentId: record.student.id,
      studentName: `${record.student.firstName} ${record.student.lastName}`,
      studentNumber: record.student.studentNumber,
      gradeSection: `${record.student.gradeLevel}-${record.student.section}`,
      status: record.status,
      statusDisplay: attendanceStatusUtils.getStatusDisplay(record.status),
      statusColor: attendanceStatusUtils.getStatusColor(record.status),
      timeIn: record.timeIn,
      timeOut: record.timeOut,
      createdAt: record.createdAt
    }))

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'attendance',
      details: {
        endpoint: 'today',
        filters: {
          gradeLevel: query.gradeLevel,
          section: query.section,
          includeDetails: query.includeDetails
        },
        resultCount: stats.total
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: {
        date: today.toISOString().split('T')[0],
        summary: {
          total: stats.total,
          present: stats.present,
          absent: stats.absent,
          late: stats.late,
          excused: stats.excused,
          attendanceRate: stats.attendanceRate
        },
        gradeBreakdown: gradeSectionStats,
        recentActivity: recentScans,
        ...(query.includeDetails && { attendanceRecords })
      },
      meta: {
        timestamp: new Date().toISOString(),
        filters: {
          gradeLevel: query.gradeLevel,
          section: query.section
        },
        includeDetails: query.includeDetails
      }
    })

  } catch (error) {
    console.error('GET /api/attendance/today error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve today\'s attendance.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve today\'s attendance.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve today\'s attendance.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve today\'s attendance.' },
    { status: 405 }
  )
}
