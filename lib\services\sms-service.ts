import { SemaphoreService } from './semaphore'
import { SMSQueueService } from './sms-queue'
import { SMSTemplateService } from './sms-template'
import { SMSCostService } from './sms-cost'
import { SMSBlacklistService } from './sms-blacklist'
import { SMSSchedulerService } from './sms-scheduler'
import { 
  SendSMSInput,
  SendBatchSMSInput,
  SendTemplatedSMSInput,
  SMSResponse,
  BatchSMSResponse,
  SMSConfiguration
} from '@/lib/types/sms'

/**
 * Main SMS Service that orchestrates all SMS-related functionality
 */
export class SMSService {
  private semaphoreService: SemaphoreService
  private queueService: SMSQueueService
  private schedulerService: SMSSchedulerService
  private static instance: SMSService | null = null

  constructor() {
    this.semaphoreService = new SemaphoreService()
    this.queueService = new SMSQueueService()
    this.schedulerService = new SMSSchedulerService()
  }

  /**
   * Get singleton instance
   */
  static getInstance(): SMSService {
    if (!SMSService.instance) {
      SMSService.instance = new SMSService()
    }
    return SMSService.instance
  }

  /**
   * Initialize SMS system
   */
  async initialize(): Promise<void> {
    try {
      // Initialize default templates
      await SMSTemplateService.initializeSystemTemplates()
      
      // Initialize default settings
      await SMSCostService.initializeDefaultSettings()
      
      // Start queue processing
      this.queueService.startProcessing()
      
      // Start scheduler
      this.schedulerService.startScheduler()
      
      console.log('SMS Service initialized successfully')
    } catch (error) {
      console.error('Failed to initialize SMS Service:', error)
      throw error
    }
  }

  /**
   * Shutdown SMS system
   */
  shutdown(): void {
    this.queueService.stopProcessing()
    this.schedulerService.stopScheduler()
    console.log('SMS Service shutdown complete')
  }

  /**
   * Send immediate SMS
   */
  async sendSMS(input: SendSMSInput, userId?: string): Promise<SMSResponse> {
    try {
      // Check if recipient is blacklisted
      const isBlacklisted = await SMSBlacklistService.isBlacklisted(input.recipientNumber)
      if (isBlacklisted) {
        return {
          success: false,
          error: 'Recipient is blacklisted'
        }
      }

      // Check daily budget
      const budgetCheck = await SMSCostService.checkDailyBudgetLimit()
      if (budgetCheck.isExceeded) {
        return {
          success: false,
          error: 'Daily SMS budget limit exceeded'
        }
      }

      // Send via Semaphore
      const result = await this.semaphoreService.sendSMS(input.recipientNumber, input.message)
      
      if (result.success) {
        // Log the SMS
        await this.queueService.addToQueue({
          ...input,
          scheduledFor: undefined // Clear scheduled time for immediate send
        }, userId)
      }

      return result
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Queue SMS for later processing
   */
  async queueSMS(input: SendSMSInput, userId?: string): Promise<{ success: boolean; smsLogId?: string; error?: string }> {
    try {
      // Check if recipient is blacklisted
      const isBlacklisted = await SMSBlacklistService.isBlacklisted(input.recipientNumber)
      if (isBlacklisted) {
        return {
          success: false,
          error: 'Recipient is blacklisted'
        }
      }

      const smsLogId = await this.queueService.addToQueue(input, userId)
      return {
        success: true,
        smsLogId
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Send batch SMS
   */
  async sendBatchSMS(input: SendBatchSMSInput, userId?: string): Promise<BatchSMSResponse> {
    try {
      // Filter out blacklisted recipients
      const validRecipients = []
      const blacklistedRecipients = []

      for (const recipient of input.recipients) {
        const isBlacklisted = await SMSBlacklistService.isBlacklisted(recipient.recipientNumber)
        if (isBlacklisted) {
          blacklistedRecipients.push(recipient.recipientNumber)
        } else {
          validRecipients.push(recipient)
        }
      }

      if (validRecipients.length === 0) {
        return {
          success: false,
          totalMessages: input.recipients.length,
          successCount: 0,
          failedCount: input.recipients.length,
          results: input.recipients.map(r => ({
            recipientNumber: r.recipientNumber,
            success: false,
            error: 'Recipient is blacklisted'
          }))
        }
      }

      // Check budget for valid recipients
      const budgetCheck = await SMSCostService.checkDailyBudgetLimit()
      const estimatedCost = validRecipients.length * parseFloat(process.env.SMS_COST_PER_MESSAGE || '2.50')
      
      if (budgetCheck.currentSpending + estimatedCost > budgetCheck.budgetLimit) {
        return {
          success: false,
          totalMessages: input.recipients.length,
          successCount: 0,
          failedCount: input.recipients.length,
          results: input.recipients.map(r => ({
            recipientNumber: r.recipientNumber,
            success: false,
            error: 'Would exceed daily budget limit'
          }))
        }
      }

      // Process batch
      const batchInput = { ...input, recipients: validRecipients }
      const result = await this.queueService.addBatchToQueue(batchInput, userId)

      // Add blacklisted recipients to failed results
      const blacklistedResults = blacklistedRecipients.map(number => ({
        recipientNumber: number,
        success: false,
        error: 'Recipient is blacklisted'
      }))

      return {
        ...result,
        results: [...result.results, ...blacklistedResults],
        totalMessages: input.recipients.length,
        failedCount: result.failedCount + blacklistedRecipients.length
      }
    } catch (error) {
      return {
        success: false,
        totalMessages: input.recipients.length,
        successCount: 0,
        failedCount: input.recipients.length,
        results: input.recipients.map(r => ({
          recipientNumber: r.recipientNumber,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }))
      }
    }
  }

  /**
   * Send templated SMS
   */
  async sendTemplatedSMS(input: SendTemplatedSMSInput, userId?: string): Promise<SMSResponse> {
    try {
      // Check if recipient is blacklisted
      const isBlacklisted = await SMSBlacklistService.isBlacklisted(input.recipientNumber)
      if (isBlacklisted) {
        return {
          success: false,
          error: 'Recipient is blacklisted'
        }
      }

      // Get and render template
      const template = await SMSTemplateService.getTemplate(input.templateId)
      if (!template) {
        return {
          success: false,
          error: 'Template not found'
        }
      }

      if (!template.isActive) {
        return {
          success: false,
          error: 'Template is not active'
        }
      }

      const message = SMSTemplateService.renderTemplate(template.content, input.variables)

      // Send SMS
      return this.sendSMS({
        recipientNumber: input.recipientNumber,
        message,
        type: input.type,
        priority: input.priority,
        scheduledFor: input.scheduledFor,
        studentId: input.studentId,
        attendanceId: input.attendanceId,
        templateId: input.templateId
      }, userId)
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Schedule SMS
   */
  async scheduleSMS(
    input: SendSMSInput & { scheduledFor: Date; name: string; description?: string },
    userId?: string
  ): Promise<{ success: boolean; jobId?: string; error?: string }> {
    try {
      const jobId = await this.schedulerService.scheduleSingleSMS(input, userId)
      return {
        success: true,
        jobId
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get SMS system status
   */
  async getSystemStatus(): Promise<{
    semaphore: { connected: boolean; balance?: number; error?: string }
    queue: { queued: number; pending: number; failed: number; sent: number; delivered: number }
    scheduler: { totalScheduled: number; dueNow: number; dueToday: number; recurringJobs: number }
    budget: { currentSpending: number; budgetLimit: number; remainingBudget: number; isExceeded: boolean }
    blacklist: { total: number; addedToday: number }
  }> {
    try {
      const [
        connectionTest,
        balance,
        queueStats,
        schedulerStats,
        budgetStatus,
        blacklistStats
      ] = await Promise.all([
        this.semaphoreService.testConnection(),
        this.semaphoreService.getBalance(),
        this.queueService.getQueueStats(),
        this.schedulerService.getSchedulerStats(),
        SMSCostService.checkDailyBudgetLimit(),
        SMSBlacklistService.getBlacklistStats()
      ])

      return {
        semaphore: {
          connected: connectionTest.success,
          balance: balance?.balance,
          error: connectionTest.error
        },
        queue: queueStats,
        scheduler: schedulerStats,
        budget: budgetStatus,
        blacklist: {
          total: blacklistStats.total,
          addedToday: blacklistStats.addedToday
        }
      }
    } catch (error) {
      throw new Error(`Failed to get system status: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get SMS configuration
   */
  async getConfiguration(): Promise<SMSConfiguration> {
    const settings = await SMSCostService.getAllSMSSettings()
    
    return {
      semaphoreApiKey: process.env.SEMAPHORE_API_KEY || '',
      semaphoreBaseUrl: process.env.SEMAPHORE_BASE_URL || 'https://api.semaphore.co',
      defaultSenderName: process.env.SEMAPHORE_SENDER_NAME || 'TSAT',
      maxRetries: parseInt(settings.max_retries || '3'),
      retryDelay: parseInt(settings.retry_delay || '60000'),
      rateLimitPerMinute: parseInt(settings.rate_limit_per_minute || '60'),
      costPerSMS: parseFloat(settings.cost_per_sms || '2.50'),
      dailyBudgetLimit: parseFloat(settings.daily_budget_limit || '500'),
      enableScheduling: true,
      enableDeliveryReceipts: true,
      enableCostTracking: settings.enable_cost_tracking === 'true',
      defaultPriority: 'NORMAL' as any
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    checks: Record<string, { status: 'pass' | 'fail'; message?: string }>
  }> {
    const checks: Record<string, { status: 'pass' | 'fail'; message?: string }> = {}

    // Check Semaphore API connection
    try {
      const connectionTest = await this.semaphoreService.testConnection()
      checks.semaphore = {
        status: connectionTest.success ? 'pass' : 'fail',
        message: connectionTest.error
      }
    } catch (error) {
      checks.semaphore = {
        status: 'fail',
        message: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Check database connectivity
    try {
      await SMSCostService.getSMSSetting('daily_budget_limit')
      checks.database = { status: 'pass' }
    } catch (error) {
      checks.database = {
        status: 'fail',
        message: error instanceof Error ? error.message : 'Database connection failed'
      }
    }

    // Check queue processing
    try {
      const queueStats = await this.queueService.getQueueStats()
      checks.queue = {
        status: 'pass',
        message: `${queueStats.queued} queued, ${queueStats.pending} pending`
      }
    } catch (error) {
      checks.queue = {
        status: 'fail',
        message: error instanceof Error ? error.message : 'Queue check failed'
      }
    }

    // Determine overall status
    const failedChecks = Object.values(checks).filter(check => check.status === 'fail').length
    let status: 'healthy' | 'degraded' | 'unhealthy'

    if (failedChecks === 0) {
      status = 'healthy'
    } else if (failedChecks === 1) {
      status = 'degraded'
    } else {
      status = 'unhealthy'
    }

    return { status, checks }
  }
}
