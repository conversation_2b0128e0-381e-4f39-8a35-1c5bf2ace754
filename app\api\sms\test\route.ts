import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { auditHelpers } from '@/lib/audit'
import { SemaphoreService } from '@/lib/services/semaphore'
import { SMSTemplateService } from '@/lib/services/sms-template'
import { SMSCostService } from '@/lib/services/sms-cost'

// Test SMS request schema
const testSMSSchema = z.object({
  testType: z.enum(['connection', 'send', 'template', 'balance', 'settings']),
  recipientNumber: z.string().optional(),
  message: z.string().max(1600).optional(),
  templateId: z.string().cuid().optional(),
  templateVariables: z.record(z.string()).optional()
})

/**
 * POST /api/sms/test
 * Test SMS configuration and functionality
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting (more restrictive for test operations)
    const rateLimitResult = await rateLimit(request, 'sms-test', 10, 60 * 1000) // 10 tests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many test requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN can run tests)
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required for SMS testing' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = testSMSSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid test request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const data = validationResult.data
    const semaphoreService = new SemaphoreService()

    let testResult: any = {
      testType: data.testType,
      timestamp: new Date().toISOString(),
      success: false
    }

    switch (data.testType) {
      case 'connection':
        // Test API connection
        const connectionTest = await semaphoreService.testConnection()
        testResult = {
          ...testResult,
          success: connectionTest.success,
          message: connectionTest.success ? 'API connection successful' : 'API connection failed',
          error: connectionTest.error,
          details: {
            apiKeyConfigured: !!process.env.SEMAPHORE_API_KEY,
            baseUrlConfigured: !!process.env.SEMAPHORE_BASE_URL,
            senderNameConfigured: !!process.env.SEMAPHORE_SENDER_NAME
          }
        }
        break

      case 'balance':
        // Test balance check
        const balance = await semaphoreService.getBalance()
        testResult = {
          ...testResult,
          success: balance !== null,
          message: balance ? `Account balance: ${balance.balance} ${balance.currency}` : 'Failed to retrieve balance',
          details: balance
        }
        break

      case 'send':
        // Test SMS sending
        if (!data.recipientNumber || !data.message) {
          return NextResponse.json(
            { success: false, error: 'Recipient number and message are required for send test' },
            { status: 400 }
          )
        }

        if (!SemaphoreService.validatePhoneNumber(data.recipientNumber)) {
          return NextResponse.json(
            { success: false, error: 'Invalid Philippine phone number format' },
            { status: 400 }
          )
        }

        const sendResult = await semaphoreService.sendSMS(data.recipientNumber, data.message)
        testResult = {
          ...testResult,
          success: sendResult.success,
          message: sendResult.success ? 'Test SMS sent successfully' : 'Failed to send test SMS',
          error: sendResult.error,
          details: {
            messageId: sendResult.messageId,
            cost: sendResult.cost,
            recipientNumber: data.recipientNumber,
            messageLength: data.message.length,
            messageInfo: SemaphoreService.getMessageInfo(data.message)
          }
        }
        break

      case 'template':
        // Test template rendering
        if (!data.templateId) {
          return NextResponse.json(
            { success: false, error: 'Template ID is required for template test' },
            { status: 400 }
          )
        }

        const template = await SMSTemplateService.getTemplate(data.templateId)
        if (!template) {
          return NextResponse.json(
            { success: false, error: 'Template not found' },
            { status: 404 }
          )
        }

        const variables = data.templateVariables || {}
        const renderedMessage = SMSTemplateService.renderTemplate(template.content, variables)
        const validation = SMSTemplateService.validateTemplate(template.content, template.variables)

        testResult = {
          ...testResult,
          success: true,
          message: 'Template test completed',
          details: {
            template: {
              id: template.id,
              name: template.name,
              category: template.category,
              content: template.content,
              variables: template.variables
            },
            providedVariables: variables,
            renderedMessage,
            messageLength: renderedMessage.length,
            messageInfo: SemaphoreService.getMessageInfo(renderedMessage),
            validation
          }
        }
        break

      case 'settings':
        // Test SMS settings and configuration
        const settings = await SMSCostService.getAllSMSSettings()
        const budgetStatus = await SMSCostService.checkDailyBudgetLimit()
        const alerts = await SMSCostService.getBudgetAlerts()

        testResult = {
          ...testResult,
          success: true,
          message: 'Settings test completed',
          details: {
            settings,
            budgetStatus,
            alerts,
            environment: {
              semaphoreApiKey: !!process.env.SEMAPHORE_API_KEY,
              semaphoreBaseUrl: process.env.SEMAPHORE_BASE_URL || 'Not configured',
              semaphoreSenderName: process.env.SEMAPHORE_SENDER_NAME || 'Not configured',
              smsCostPerMessage: process.env.SMS_COST_PER_MESSAGE || 'Not configured'
            }
          }
        }
        break

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid test type' },
          { status: 400 }
        )
    }

    // Log audit trail
    await auditHelpers.createAuditLog({
      userId: user.id,
      action: 'VIEW' as any,
      resource: 'sms_test',
      details: {
        testType: data.testType,
        success: testResult.success,
        recipientNumber: data.recipientNumber,
        templateId: data.templateId
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    })

    return NextResponse.json(testResult)

  } catch (error) {
    console.error('SMS test error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/sms/test
 * Get available test types and requirements
 */
export async function GET(request: NextRequest) {
  try {
    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const testTypes = [
      {
        type: 'connection',
        name: 'API Connection Test',
        description: 'Test connection to Semaphore API',
        requiredFields: [],
        optionalFields: []
      },
      {
        type: 'balance',
        name: 'Balance Check Test',
        description: 'Check Semaphore account balance',
        requiredFields: [],
        optionalFields: []
      },
      {
        type: 'send',
        name: 'SMS Send Test',
        description: 'Send a test SMS message',
        requiredFields: ['recipientNumber', 'message'],
        optionalFields: []
      },
      {
        type: 'template',
        name: 'Template Test',
        description: 'Test SMS template rendering',
        requiredFields: ['templateId'],
        optionalFields: ['templateVariables']
      },
      {
        type: 'settings',
        name: 'Settings Test',
        description: 'Test SMS configuration and settings',
        requiredFields: [],
        optionalFields: []
      }
    ]

    return NextResponse.json({
      success: true,
      testTypes,
      availableVariables: SMSTemplateService.getAvailableVariables()
    })

  } catch (error) {
    console.error('Get SMS test info error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to run tests or GET to see available test types.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to run tests or GET to see available test types.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to run tests or GET to see available test types.' },
    { status: 405 }
  )
}
