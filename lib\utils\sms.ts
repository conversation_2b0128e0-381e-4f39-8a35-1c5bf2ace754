import { prisma } from '@/lib/db'
import { SMSStatus, AttendanceStatus } from '../../generated/prisma'

// SMS notification utilities
export const smsUtils = {
  // Send SMS notification for attendance
  sendAttendanceNotification: async (
    studentId: string,
    attendanceId: string,
    status: AttendanceStatus,
    timeIn?: Date
  ) => {
    try {
      // Get student and guardian information
      const student = await prisma.student.findUnique({
        where: { id: studentId },
        select: {
          firstName: true,
          lastName: true,
          guardianName: true,
          guardianContact: true,
          gradeLevel: true,
          section: true
        }
      })

      if (!student) {
        throw new Error('Student not found')
      }

      // Generate message based on attendance status
      const message = generateAttendanceMessage(student, status, timeIn)

      // Create SMS log entry
      const smsLog = await prisma.sMSLog.create({
        data: {
          recipientNumber: student.guardianContact,
          message,
          status: SMSStatus.PENDING,
          studentId,
          attendanceId
        }
      })

      // Send SMS (integrate with SMS provider)
      const sendResult = await sendSMS(student.guardianContact, message)

      // Update SMS log with result
      await prisma.sMSLog.update({
        where: { id: smsLog.id },
        data: {
          status: sendResult.success ? SMSStatus.SENT : SMSStatus.FAILED,
          sentAt: sendResult.success ? new Date() : null
        }
      })

      return {
        success: sendResult.success,
        smsLogId: smsLog.id,
        message: sendResult.message
      }
    } catch (error) {
      console.error('SMS notification error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'SMS notification failed'
      }
    }
  },

  // Send bulk SMS notifications
  sendBulkNotifications: async (notifications: Array<{
    studentId: string
    attendanceId: string
    status: AttendanceStatus
    timeIn?: Date
  }>) => {
    const results = []

    for (const notification of notifications) {
      const result = await smsUtils.sendAttendanceNotification(
        notification.studentId,
        notification.attendanceId,
        notification.status,
        notification.timeIn
      )
      results.push({
        studentId: notification.studentId,
        ...result
      })
    }

    return results
  },

  // Get SMS delivery status
  checkDeliveryStatus: async (smsLogId: string) => {
    try {
      const smsLog = await prisma.sMSLog.findUnique({
        where: { id: smsLogId }
      })

      if (!smsLog) {
        return { found: false }
      }

      // Check with SMS provider for delivery status
      // This would integrate with your SMS provider's API
      const deliveryStatus = await checkSMSDeliveryStatus(smsLog.id)

      if (deliveryStatus.delivered && smsLog.status !== SMSStatus.DELIVERED) {
        await prisma.sMSLog.update({
          where: { id: smsLogId },
          data: {
            status: SMSStatus.DELIVERED,
            deliveredAt: new Date()
          }
        })
      }

      return {
        found: true,
        status: deliveryStatus.delivered ? SMSStatus.DELIVERED : smsLog.status,
        sentAt: smsLog.sentAt,
        deliveredAt: deliveryStatus.delivered ? new Date() : smsLog.deliveredAt
      }
    } catch (error) {
      console.error('SMS delivery check error:', error)
      return { found: false, error: 'Failed to check delivery status' }
    }
  },

  // Retry failed SMS
  retryFailedSMS: async (smsLogId: string) => {
    try {
      const smsLog = await prisma.sMSLog.findUnique({
        where: { id: smsLogId }
      })

      if (!smsLog || smsLog.status !== SMSStatus.FAILED) {
        return { success: false, error: 'SMS not found or not in failed state' }
      }

      // Retry sending
      const sendResult = await sendSMS(smsLog.recipientNumber, smsLog.message)

      // Update status
      await prisma.sMSLog.update({
        where: { id: smsLogId },
        data: {
          status: sendResult.success ? SMSStatus.SENT : SMSStatus.FAILED,
          sentAt: sendResult.success ? new Date() : null
        }
      })

      return {
        success: sendResult.success,
        message: sendResult.message
      }
    } catch (error) {
      console.error('SMS retry error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'SMS retry failed'
      }
    }
  }
}

// Message generation utilities
const generateAttendanceMessage = (
  student: {
    firstName: string
    lastName: string
    guardianName: string
    gradeLevel: string
    section: string
  },
  status: AttendanceStatus,
  timeIn?: Date
): string => {
  const studentName = `${student.firstName} ${student.lastName}`
  const timeStr = timeIn ? timeIn.toLocaleTimeString('en-PH', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: true 
  }) : ''

  switch (status) {
    case AttendanceStatus.PRESENT:
      return `Good day ${student.guardianName}! Your child ${studentName} (${student.gradeLevel}-${student.section}) has arrived at school at ${timeStr}. Have a great day!`
    
    case AttendanceStatus.LATE:
      return `Dear ${student.guardianName}, your child ${studentName} (${student.gradeLevel}-${student.section}) arrived late at school at ${timeStr}. Please ensure punctuality. Thank you.`
    
    case AttendanceStatus.ABSENT:
      return `Dear ${student.guardianName}, your child ${studentName} (${student.gradeLevel}-${student.section}) is marked absent today. If this is incorrect, please contact the school. Thank you.`
    
    case AttendanceStatus.EXCUSED:
      return `Dear ${student.guardianName}, your child ${studentName} (${student.gradeLevel}-${student.section}) has an excused absence today. Thank you for informing the school.`
    
    default:
      return `Dear ${student.guardianName}, attendance update for ${studentName} (${student.gradeLevel}-${student.section}). Please contact the school for details.`
  }
}

// SMS provider integration (placeholder - implement with your SMS provider)
const sendSMS = async (phoneNumber: string, message: string): Promise<{
  success: boolean
  message?: string
  messageId?: string
}> => {
  try {
    // This is a placeholder implementation
    // Replace with actual SMS provider integration (e.g., Twilio, Semaphore, etc.)
    
    // Validate phone number format
    if (!phoneNumber.match(/^(\+63|0)[0-9]{10}$/)) {
      return {
        success: false,
        message: 'Invalid phone number format'
      }
    }

    // Simulate SMS sending (replace with actual API call)
    console.log(`Sending SMS to ${phoneNumber}: ${message}`)
    
    // For development/testing, always return success
    // In production, implement actual SMS provider API call
    return {
      success: true,
      message: 'SMS sent successfully',
      messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  } catch (error) {
    console.error('SMS sending error:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'SMS sending failed'
    }
  }
}

// SMS delivery status check (placeholder - implement with your SMS provider)
const checkSMSDeliveryStatus = async (smsLogId: string): Promise<{
  delivered: boolean
  deliveredAt?: Date
}> => {
  try {
    // This is a placeholder implementation
    // Replace with actual SMS provider delivery status check
    
    // For development/testing, simulate delivery after 30 seconds
    const smsLog = await prisma.sMSLog.findUnique({
      where: { id: smsLogId }
    })

    if (!smsLog || !smsLog.sentAt) {
      return { delivered: false }
    }

    const timeSinceSent = Date.now() - smsLog.sentAt.getTime()
    const delivered = timeSinceSent > 30000 // 30 seconds

    return {
      delivered,
      deliveredAt: delivered ? new Date() : undefined
    }
  } catch (error) {
    console.error('SMS delivery status check error:', error)
    return { delivered: false }
  }
}

// SMS template utilities
export const smsTemplateUtils = {
  // Get available SMS templates
  getTemplates: () => {
    return {
      attendance_present: 'Good day {guardianName}! Your child {studentName} ({gradeSection}) has arrived at school at {time}. Have a great day!',
      attendance_late: 'Dear {guardianName}, your child {studentName} ({gradeSection}) arrived late at school at {time}. Please ensure punctuality. Thank you.',
      attendance_absent: 'Dear {guardianName}, your child {studentName} ({gradeSection}) is marked absent today. If this is incorrect, please contact the school. Thank you.',
      attendance_excused: 'Dear {guardianName}, your child {studentName} ({gradeSection}) has an excused absence today. Thank you for informing the school.',
      general_reminder: 'Dear {guardianName}, this is a reminder regarding {studentName} ({gradeSection}). Please contact the school for more information.'
    }
  },

  // Process template with variables
  processTemplate: (template: string, variables: Record<string, string>) => {
    let processed = template
    Object.entries(variables).forEach(([key, value]) => {
      processed = processed.replace(new RegExp(`{${key}}`, 'g'), value)
    })
    return processed
  }
}

// SMS statistics utilities
export const smsStatsUtils = {
  // Get SMS statistics for a date range
  getSMSStats: async (startDate: Date, endDate: Date) => {
    const stats = await prisma.sMSLog.groupBy({
      by: ['status'],
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      _count: {
        status: true
      }
    })

    const result = {
      total: 0,
      pending: 0,
      sent: 0,
      delivered: 0,
      failed: 0,
      deliveryRate: 0,
      successRate: 0
    }

    stats.forEach(stat => {
      result.total += stat._count.status
      switch (stat.status) {
        case SMSStatus.PENDING:
          result.pending = stat._count.status
          break
        case SMSStatus.SENT:
          result.sent = stat._count.status
          break
        case SMSStatus.DELIVERED:
          result.delivered = stat._count.status
          break
        case SMSStatus.FAILED:
          result.failed = stat._count.status
          break
      }
    })

    if (result.total > 0) {
      result.successRate = ((result.sent + result.delivered) / result.total) * 100
      result.deliveryRate = (result.delivered / result.total) * 100
    }

    return result
  },

  // Get failed SMS messages for retry
  getFailedSMS: async (limit: number = 50) => {
    return await prisma.sMSLog.findMany({
      where: {
        status: SMSStatus.FAILED
      },
      include: {
        student: {
          select: {
            firstName: true,
            lastName: true,
            studentNumber: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    })
  }
}
