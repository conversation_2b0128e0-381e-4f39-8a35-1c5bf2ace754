import { NextRequest, NextResponse } from 'next/server'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { prisma } from '@/lib/db'
import { SMSStatus, SMSType, SMSPriority } from '../../../../generated/prisma'

/**
 * GET /api/sms/logs
 * Get SMS history and logs
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'sms-logs', 100, 60 * 1000) // 100 requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse query parameters
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 100) // Max 100 per page
    const status = url.searchParams.get('status') as SMSStatus | null
    const type = url.searchParams.get('type') as SMSType | null
    const priority = url.searchParams.get('priority') as SMSPriority | null
    const recipientNumber = url.searchParams.get('recipientNumber')
    const studentId = url.searchParams.get('studentId')
    const batchId = url.searchParams.get('batchId')
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')
    const includeStudent = url.searchParams.get('includeStudent') === 'true'
    const includeTemplate = url.searchParams.get('includeTemplate') === 'true'
    const includeUser = url.searchParams.get('includeUser') === 'true'

    // Build where clause
    const where: any = {}

    if (status && Object.values(SMSStatus).includes(status)) {
      where.status = status
    }

    if (type && Object.values(SMSType).includes(type)) {
      where.type = type
    }

    if (priority && Object.values(SMSPriority).includes(priority)) {
      where.priority = priority
    }

    if (recipientNumber) {
      where.recipientNumber = {
        contains: recipientNumber
      }
    }

    if (studentId) {
      where.studentId = studentId
    }

    if (batchId) {
      where.batchId = batchId
    }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate)
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate)
      }
    }

    // Build include clause
    const include: any = {}

    if (includeStudent) {
      include.student = {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          studentNumber: true,
          gradeLevel: true,
          section: true
        }
      }
    }

    if (includeTemplate) {
      include.template = {
        select: {
          id: true,
          name: true,
          category: true
        }
      }
    }

    if (includeUser) {
      include.user = {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
          role: true
        }
      }
    }

    // Get total count
    const totalCount = await prisma.sMSLog.count({ where })

    // Get logs with pagination
    const logs = await prisma.sMSLog.findMany({
      where,
      include,
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit)
    const hasNextPage = page < totalPages
    const hasPreviousPage = page > 1

    // Get summary statistics for the filtered results
    const statusStats = await prisma.sMSLog.groupBy({
      by: ['status'],
      where,
      _count: { status: true },
      _sum: { cost: true }
    })

    const summary = {
      total: totalCount,
      totalCost: statusStats.reduce((sum, stat) => sum + (stat._sum.cost || 0), 0),
      byStatus: statusStats.reduce((acc, stat) => {
        acc[stat.status] = {
          count: stat._count.status,
          cost: stat._sum.cost || 0
        }
        return acc
      }, {} as Record<string, { count: number; cost: number }>)
    }

    return NextResponse.json({
      success: true,
      logs,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPreviousPage
      },
      summary,
      filters: {
        status,
        type,
        priority,
        recipientNumber,
        studentId,
        batchId,
        startDate,
        endDate
      }
    })

  } catch (error) {
    console.error('Get SMS logs error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/sms/logs
 * Delete old SMS logs (admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Only admins can delete logs
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Parse query parameters
    const url = new URL(request.url)
    const olderThanDays = parseInt(url.searchParams.get('olderThanDays') || '90')
    const status = url.searchParams.get('status') as SMSStatus | null

    if (olderThanDays < 30) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete logs newer than 30 days' },
        { status: 400 }
      )
    }

    // Calculate cutoff date
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    // Build where clause
    const where: any = {
      createdAt: {
        lt: cutoffDate
      }
    }

    if (status && Object.values(SMSStatus).includes(status)) {
      where.status = status
    }

    // Count logs to be deleted
    const countToDelete = await prisma.sMSLog.count({ where })

    if (countToDelete === 0) {
      return NextResponse.json({
        success: true,
        message: 'No logs found matching the criteria',
        deletedCount: 0
      })
    }

    // Delete logs
    const deleteResult = await prisma.sMSLog.deleteMany({ where })

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${deleteResult.count} SMS logs`,
      deletedCount: deleteResult.count,
      criteria: {
        olderThanDays,
        status,
        cutoffDate
      }
    })

  } catch (error) {
    console.error('Delete SMS logs error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve logs or DELETE to remove old logs.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve logs or DELETE to remove old logs.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve logs or DELETE to remove old logs.' },
    { status: 405 }
  )
}
