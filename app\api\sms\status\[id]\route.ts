import { NextRequest, NextResponse } from 'next/server'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { prisma } from '@/lib/db'
import { SemaphoreService } from '@/lib/services/semaphore'
import { SMSStatus } from '../../../../../generated/prisma'

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * GET /api/sms/status/[id]
 * Check SMS delivery status
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'sms-status', 100, 60 * 1000) // 100 status checks per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many status check requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const smsLogId = params.id

    // Validate SMS log ID format
    if (!smsLogId || typeof smsLogId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid SMS log ID' },
        { status: 400 }
      )
    }

    // Get SMS log from database
    const smsLog = await prisma.sMSLog.findUnique({
      where: { id: smsLogId },
      include: {
        student: {
          select: {
            firstName: true,
            lastName: true,
            studentNumber: true
          }
        }
      }
    })

    if (!smsLog) {
      return NextResponse.json(
        { success: false, error: 'SMS log not found' },
        { status: 404 }
      )
    }

    // If SMS has a message ID and is not already delivered, check with Semaphore
    if (smsLog.messageId && smsLog.status !== SMSStatus.DELIVERED && smsLog.status !== SMSStatus.FAILED) {
      try {
        const semaphoreService = new SemaphoreService()
        const deliveryStatus = await semaphoreService.checkDeliveryStatus(smsLog.messageId)

        // Update database if status changed
        if (deliveryStatus.status === 'delivered' && smsLog.status !== SMSStatus.DELIVERED) {
          await prisma.sMSLog.update({
            where: { id: smsLogId },
            data: {
              status: SMSStatus.DELIVERED,
              deliveredAt: deliveryStatus.deliveredAt || new Date()
            }
          })
          
          // Update the local object for response
          smsLog.status = SMSStatus.DELIVERED
          smsLog.deliveredAt = deliveryStatus.deliveredAt || new Date()

        } else if (deliveryStatus.status === 'failed' && smsLog.status !== SMSStatus.FAILED) {
          await prisma.sMSLog.update({
            where: { id: smsLogId },
            data: {
              status: SMSStatus.FAILED,
              failedAt: new Date(),
              errorMessage: deliveryStatus.failureReason || 'Delivery failed'
            }
          })

          // Update the local object for response
          smsLog.status = SMSStatus.FAILED
          smsLog.failedAt = new Date()
          smsLog.errorMessage = deliveryStatus.failureReason || 'Delivery failed'
        }

      } catch (error) {
        console.error('Error checking delivery status:', error)
        // Continue with current database status if API check fails
      }
    }

    // Calculate delivery time if available
    let deliveryTimeMs: number | undefined
    if (smsLog.sentAt && smsLog.deliveredAt) {
      deliveryTimeMs = smsLog.deliveredAt.getTime() - smsLog.sentAt.getTime()
    }

    // Prepare response
    const response = {
      success: true,
      smsLog: {
        id: smsLog.id,
        recipientNumber: smsLog.recipientNumber,
        message: smsLog.message,
        status: smsLog.status,
        type: smsLog.type,
        priority: smsLog.priority,
        sentAt: smsLog.sentAt,
        deliveredAt: smsLog.deliveredAt,
        failedAt: smsLog.failedAt,
        scheduledFor: smsLog.scheduledFor,
        retryCount: smsLog.retryCount,
        maxRetries: smsLog.maxRetries,
        cost: smsLog.cost,
        messageId: smsLog.messageId,
        errorMessage: smsLog.errorMessage,
        batchId: smsLog.batchId,
        createdAt: smsLog.createdAt,
        updatedAt: smsLog.updatedAt,
        deliveryTimeMs,
        student: smsLog.student ? {
          name: `${smsLog.student.firstName} ${smsLog.student.lastName}`,
          studentNumber: smsLog.student.studentNumber
        } : null
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('SMS status check error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/sms/status/[id]
 * Update SMS status manually (admin only)
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Only admins can manually update SMS status
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const smsLogId = params.id
    const body = await request.json()

    const { status, errorMessage } = body

    if (!Object.values(SMSStatus).includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status value' },
        { status: 400 }
      )
    }

    // Update SMS log
    const updateData: any = { status }

    if (status === SMSStatus.DELIVERED) {
      updateData.deliveredAt = new Date()
    } else if (status === SMSStatus.FAILED) {
      updateData.failedAt = new Date()
      if (errorMessage) {
        updateData.errorMessage = errorMessage
      }
    } else if (status === SMSStatus.SENT) {
      updateData.sentAt = new Date()
    }

    const updatedSmsLog = await prisma.sMSLog.update({
      where: { id: smsLogId },
      data: updateData
    })

    return NextResponse.json({
      success: true,
      message: 'SMS status updated successfully',
      smsLog: updatedSmsLog
    })

  } catch (error) {
    console.error('SMS status update error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to check status or PUT to update status.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to check status or PUT to update status.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to check status or PUT to update status.' },
    { status: 405 }
  )
}
