import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { auditHelpers } from '@/lib/audit'
import { SMSQueueService } from '@/lib/services/sms-queue'
import { SemaphoreService } from '@/lib/services/semaphore'
import { SMSCostService } from '@/lib/services/sms-cost'
import { SMSType, SMSPriority } from '../../../../generated/prisma'

// Batch SMS recipient schema
const batchRecipientSchema = z.object({
  recipientNumber: z.string().min(1, 'Recipient number is required'),
  message: z.string().min(1, 'Message is required').max(1600, 'Message too long'),
  studentId: z.string().cuid().optional(),
  attendanceId: z.string().cuid().optional()
})

// Batch SMS request schema
const batchSMSSchema = z.object({
  recipients: z.array(batchRecipientSchema).min(1, 'At least one recipient is required').max(100, 'Maximum 100 recipients per batch'),
  type: z.nativeEnum(SMSType).optional(),
  priority: z.nativeEnum(SMSPriority).optional(),
  scheduledFor: z.string().datetime().optional(),
  templateId: z.string().cuid().optional(),
  sendImmediately: z.boolean().default(false)
})

/**
 * POST /api/sms/batch
 * Send batch SMS notifications
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting (more restrictive for batch operations)
    const rateLimitResult = await rateLimit(request, 'sms-batch', 5, 60 * 1000) // 5 batch requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many batch SMS requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and TEACHER can send batch SMS)
    if (!['ADMIN', 'TEACHER'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions for batch SMS' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = batchSMSSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid batch SMS data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const data = validationResult.data

    // Validate all phone numbers
    const invalidNumbers = data.recipients.filter(
      recipient => !SemaphoreService.validatePhoneNumber(recipient.recipientNumber)
    )

    if (invalidNumbers.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid phone number format detected',
          details: {
            invalidNumbers: invalidNumbers.map(r => r.recipientNumber)
          }
        },
        { status: 400 }
      )
    }

    // Check daily budget limit
    const budgetCheck = await SMSCostService.checkDailyBudgetLimit()
    const estimatedCost = data.recipients.length * parseFloat(process.env.SMS_COST_PER_MESSAGE || '2.50')
    
    if (budgetCheck.currentSpending + estimatedCost > budgetCheck.budgetLimit) {
      return NextResponse.json(
        {
          success: false,
          error: 'Batch SMS would exceed daily budget limit',
          details: {
            currentSpending: budgetCheck.currentSpending,
            estimatedCost,
            budgetLimit: budgetCheck.budgetLimit,
            remainingBudget: budgetCheck.remainingBudget
          }
        },
        { status: 429 }
      )
    }

    const smsQueue = new SMSQueueService()

    if (data.sendImmediately) {
      // Send immediately using Semaphore bulk API
      const semaphoreService = new SemaphoreService()
      const messages = data.recipients.map(recipient => ({
        recipientNumber: recipient.recipientNumber,
        message: recipient.message
      }))

      const results = await semaphoreService.sendBulkSMS(messages)
      
      // Process results and create logs
      let successCount = 0
      let failedCount = 0
      const processedResults = []

      for (let i = 0; i < results.length; i++) {
        const result = results[i]
        const recipient = data.recipients[i]

        if (result.success) {
          successCount++
          // Log successful SMS
          const smsLogId = await smsQueue.addToQueue({
            recipientNumber: recipient.recipientNumber,
            message: recipient.message,
            type: data.type,
            priority: data.priority,
            studentId: recipient.studentId,
            attendanceId: recipient.attendanceId,
            templateId: data.templateId
          }, user.id)

          processedResults.push({
            recipientNumber: recipient.recipientNumber,
            success: true,
            messageId: result.messageId,
            smsLogId,
            cost: result.cost
          })
        } else {
          failedCount++
          processedResults.push({
            recipientNumber: recipient.recipientNumber,
            success: false,
            error: result.error
          })
        }
      }

      const totalCost = results.reduce((sum, result) => sum + (result.cost || 0), 0)

      // Log audit trail
      await auditHelpers.createAuditLog({
        userId: user.id,
        action: 'CREATE' as any,
        resource: 'sms_batch',
        details: {
          totalRecipients: data.recipients.length,
          successCount,
          failedCount,
          totalCost,
          type: data.type,
          immediate: true
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      })

      return NextResponse.json({
        success: successCount > 0,
        totalMessages: data.recipients.length,
        successCount,
        failedCount,
        totalCost,
        results: processedResults,
        message: `Batch SMS completed: ${successCount} sent, ${failedCount} failed`
      })

    } else {
      // Add to queue for processing
      const batchResult = await smsQueue.addBatchToQueue({
        recipients: data.recipients,
        type: data.type,
        priority: data.priority,
        scheduledFor: data.scheduledFor ? new Date(data.scheduledFor) : undefined,
        templateId: data.templateId
      }, user.id)

      // Log audit trail
      await auditHelpers.createAuditLog({
        userId: user.id,
        action: 'CREATE' as any,
        resource: 'sms_batch',
        details: {
          batchId: batchResult.batchId,
          totalRecipients: data.recipients.length,
          successCount: batchResult.successCount,
          failedCount: batchResult.failedCount,
          type: data.type,
          queued: true,
          scheduledFor: data.scheduledFor
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      })

      return NextResponse.json({
        success: batchResult.success,
        batchId: batchResult.batchId,
        totalMessages: batchResult.totalMessages,
        successCount: batchResult.successCount,
        failedCount: batchResult.failedCount,
        results: batchResult.results,
        message: data.scheduledFor 
          ? 'Batch SMS scheduled successfully' 
          : 'Batch SMS queued for sending'
      })
    }

  } catch (error) {
    console.error('Batch SMS error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send batch SMS.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send batch SMS.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send batch SMS.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send batch SMS.' },
    { status: 405 }
  )
}
