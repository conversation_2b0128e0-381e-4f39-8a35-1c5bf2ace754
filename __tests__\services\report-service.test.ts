import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { ReportService } from '@/lib/services/report-service'
import { SF2GeneratorService } from '@/lib/services/sf2-generator'
import { SF4GeneratorService } from '@/lib/services/sf4-generator'
import { ComplianceValidatorService } from '@/lib/services/compliance-validator'
import { FileStorageService } from '@/lib/services/file-storage'
import { ReportAnalyticsService } from '@/lib/services/report-analytics'
import { prisma } from '@/lib/db'

// Mock dependencies
jest.mock('@/lib/db')
jest.mock('@/lib/services/sf2-generator')
jest.mock('@/lib/services/sf4-generator')
jest.mock('@/lib/services/compliance-validator')
jest.mock('@/lib/services/file-storage')
jest.mock('@/lib/services/report-analytics')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('ReportService', () => {
  let reportService: ReportService
  let mockSF2Generator: jest.Mocked<SF2GeneratorService>
  let mockSF4Generator: jest.Mocked<SF4GeneratorService>
  let mockComplianceValidator: jest.Mocked<ComplianceValidatorService>
  let mockFileStorage: jest.Mocked<FileStorageService>
  let mockAnalytics: jest.Mocked<ReportAnalyticsService>

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()

    // Setup service mocks
    mockSF2Generator = {
      generateSF2Data: jest.fn(),
      validateSF2Data: jest.fn(),
      generateSF2Template: jest.fn()
    } as any

    mockSF4Generator = {
      generateSF4Data: jest.fn(),
      validateSF4Data: jest.fn(),
      generateSF4Template: jest.fn()
    } as any

    mockComplianceValidator = {
      validateReport: jest.fn(),
      validateTemplate: jest.fn(),
      getComplianceRequirements: jest.fn()
    } as any

    mockFileStorage = {
      storeFile: jest.fn(),
      getFile: jest.fn(),
      deleteFile: jest.fn(),
      getStorageStats: jest.fn()
    } as any

    mockAnalytics = {
      recordGeneration: jest.fn(),
      recordDownload: jest.fn(),
      recordFailure: jest.fn()
    } as any

    // Mock static getInstance methods
    jest.mocked(SF2GeneratorService.getInstance).mockReturnValue(mockSF2Generator)
    jest.mocked(SF4GeneratorService.getInstance).mockReturnValue(mockSF4Generator)
    jest.mocked(ComplianceValidatorService.getInstance).mockReturnValue(mockComplianceValidator)
    jest.mocked(FileStorageService.getInstance).mockReturnValue(mockFileStorage)
    jest.mocked(ReportAnalyticsService.getInstance).mockReturnValue(mockAnalytics)

    reportService = ReportService.getInstance()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('generateReport', () => {
    const mockReportInput = {
      name: 'Test Report',
      description: 'Test Description',
      type: 'SF2' as const,
      templateId: 'template-123',
      parameters: {
        reportType: 'SF2' as const,
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-01')
        },
        filters: {
          grades: ['Grade 7'],
          sections: ['A'],
          courses: [],
          teachers: [],
          students: []
        },
        format: 'pdf' as const,
        includeCharts: false,
        includeStatistics: true,
        includeSignatures: true
      },
      userId: 'user-123'
    }

    it('should successfully generate an SF2 report', async () => {
      // Setup mocks
      const mockReportData = {
        schoolInfo: { name: 'Test School', division: 'Test Division', region: 'Test Region' },
        reportDate: new Date('2024-01-01'),
        gradeSection: 'Grade 7-A',
        attendanceSummary: {
          enrollment: { male: 15, female: 10, total: 25 },
          present: { male: 14, female: 9, total: 23 },
          absent: { male: 1, female: 1, total: 2 },
          late: { male: 0, female: 0, total: 0 },
          excused: { male: 0, female: 0, total: 0 }
        },
        detailedAttendance: [],
        classAdviser: { name: 'Test Teacher' },
        signatures: {
          classAdviser: { name: 'Test Teacher', date: new Date() },
          principal: { name: 'Test Principal', date: new Date() }
        }
      }

      const mockValidationResult = {
        isValid: true,
        errors: [],
        warnings: [],
        complianceScore: 95
      }

      const mockComplianceResult = {
        isCompliant: true,
        score: 95,
        issues: [],
        warnings: [],
        recommendations: []
      }

      const mockFileResult = {
        fileUrl: 'reports/test-report.pdf',
        filePath: '/storage/reports/test-report.pdf',
        fileName: 'test-report.pdf',
        fileSize: 1024000,
        mimeType: 'application/pdf'
      }

      mockSF2Generator.generateSF2Data.mockResolvedValue(mockReportData)
      mockSF2Generator.validateSF2Data.mockResolvedValue(mockValidationResult)
      mockComplianceValidator.validateReport.mockResolvedValue(mockComplianceResult)
      mockFileStorage.storeFile.mockResolvedValue(mockFileResult)

      mockPrisma.generatedReport.create.mockResolvedValue({
        id: 'report-123',
        name: mockReportInput.name,
        description: mockReportInput.description,
        type: 'SF2',
        status: 'COMPLETED',
        fileUrl: mockFileResult.fileUrl,
        fileName: mockFileResult.fileName,
        fileSize: mockFileResult.fileSize,
        generatedAt: new Date(),
        completedAt: new Date(),
        generatedBy: mockReportInput.userId,
        downloadCount: 0,
        isArchived: false,
        parameters: JSON.stringify(mockReportInput.parameters)
      } as any)

      // Execute
      const result = await reportService.generateReport(mockReportInput)

      // Assertions
      expect(result.success).toBe(true)
      expect(result.reportId).toBe('report-123')
      expect(result.fileUrl).toBe(mockFileResult.fileUrl)
      expect(result.complianceIssues).toEqual([])

      expect(mockSF2Generator.generateSF2Data).toHaveBeenCalledWith(mockReportInput.parameters)
      expect(mockSF2Generator.validateSF2Data).toHaveBeenCalledWith(mockReportData)
      expect(mockComplianceValidator.validateReport).toHaveBeenCalled()
      expect(mockFileStorage.storeFile).toHaveBeenCalled()
      expect(mockAnalytics.recordGeneration).toHaveBeenCalled()
    })

    it('should handle validation errors', async () => {
      const mockValidationResult = {
        isValid: false,
        errors: ['Missing required field: schoolInfo'],
        warnings: [],
        complianceScore: 45
      }

      mockSF2Generator.generateSF2Data.mockResolvedValue({} as any)
      mockSF2Generator.validateSF2Data.mockResolvedValue(mockValidationResult)

      const result = await reportService.generateReport(mockReportInput)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Validation failed')
      expect(mockAnalytics.recordFailure).toHaveBeenCalled()
    })

    it('should handle file storage errors', async () => {
      const mockReportData = { schoolInfo: { name: 'Test' } }
      const mockValidationResult = { isValid: true, errors: [], warnings: [], complianceScore: 95 }
      const mockComplianceResult = { isCompliant: true, score: 95, issues: [], warnings: [], recommendations: [] }

      mockSF2Generator.generateSF2Data.mockResolvedValue(mockReportData as any)
      mockSF2Generator.validateSF2Data.mockResolvedValue(mockValidationResult)
      mockComplianceValidator.validateReport.mockResolvedValue(mockComplianceResult)
      mockFileStorage.storeFile.mockRejectedValue(new Error('Storage error'))

      const result = await reportService.generateReport(mockReportInput)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Storage error')
      expect(mockAnalytics.recordFailure).toHaveBeenCalled()
    })
  })

  describe('getReport', () => {
    it('should retrieve a report successfully', async () => {
      const mockReport = {
        id: 'report-123',
        name: 'Test Report',
        type: 'SF2',
        status: 'COMPLETED',
        generatedBy: 'user-123',
        fileUrl: 'reports/test.pdf',
        generatedAt: new Date(),
        downloadCount: 5
      }

      mockPrisma.generatedReport.findFirst.mockResolvedValue(mockReport as any)

      const result = await reportService.getReport('report-123', 'user-123')

      expect(result).toEqual(mockReport)
      expect(mockPrisma.generatedReport.findFirst).toHaveBeenCalledWith({
        where: {
          id: 'report-123',
          generatedBy: 'user-123'
        }
      })
    })

    it('should return null for non-existent report', async () => {
      mockPrisma.generatedReport.findFirst.mockResolvedValue(null)

      const result = await reportService.getReport('non-existent', 'user-123')

      expect(result).toBeNull()
    })
  })

  describe('listReports', () => {
    it('should list reports with filters', async () => {
      const mockReports = [
        { id: 'report-1', name: 'Report 1', type: 'SF2', status: 'COMPLETED' },
        { id: 'report-2', name: 'Report 2', type: 'SF4', status: 'COMPLETED' }
      ]

      mockPrisma.generatedReport.findMany.mockResolvedValue(mockReports as any)
      mockPrisma.generatedReport.count.mockResolvedValue(2)

      const filters = {
        type: 'SF2' as const,
        limit: 10,
        offset: 0
      }

      const result = await reportService.listReports('user-123', filters)

      expect(result.reports).toEqual(mockReports)
      expect(result.total).toBe(2)
      expect(mockPrisma.generatedReport.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          generatedBy: 'user-123',
          type: 'SF2'
        }),
        take: 10,
        skip: 0,
        orderBy: { generatedAt: 'desc' }
      })
    })
  })

  describe('deleteReport', () => {
    it('should delete a report and its file', async () => {
      const mockReport = {
        id: 'report-123',
        fileUrl: 'reports/test.pdf',
        generatedBy: 'user-123'
      }

      mockPrisma.generatedReport.findFirst.mockResolvedValue(mockReport as any)
      mockPrisma.generatedReport.delete.mockResolvedValue(mockReport as any)
      mockFileStorage.deleteFile.mockResolvedValue(true)

      const result = await reportService.deleteReport('report-123', 'user-123')

      expect(result).toBe(true)
      expect(mockFileStorage.deleteFile).toHaveBeenCalledWith('reports/test.pdf')
      expect(mockPrisma.generatedReport.delete).toHaveBeenCalledWith({
        where: { id: 'report-123' }
      })
    })

    it('should return false for non-existent report', async () => {
      mockPrisma.generatedReport.findFirst.mockResolvedValue(null)

      const result = await reportService.deleteReport('non-existent', 'user-123')

      expect(result).toBe(false)
    })
  })
})
