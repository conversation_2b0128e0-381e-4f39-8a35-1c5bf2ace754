import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { SF2GeneratorService } from '@/lib/services/sf2-generator'
import { prisma } from '@/lib/db'
import { AttendanceStatus, StudentStatus } from '../../generated/prisma'

// Mock dependencies
jest.mock('@/lib/db')

const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('SF2GeneratorService', () => {
  let sf2Generator: SF2GeneratorService

  beforeEach(() => {
    jest.clearAllMocks()
    sf2Generator = SF2GeneratorService.getInstance()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('generateSF2Data', () => {
    const mockParameters = {
      reportType: 'SF2' as const,
      dateRange: {
        start: new Date('2024-01-15T00:00:00Z'),
        end: new Date('2024-01-15T23:59:59Z')
      },
      filters: {
        grades: ['Grade 7'],
        sections: ['A'],
        courses: [],
        teachers: ['teacher-123'],
        students: []
      },
      format: 'pdf' as const,
      includeCharts: false,
      includeStatistics: true,
      includeSignatures: true
    }

    const mockAttendanceData = [
      {
        id: 'attendance-1',
        date: new Date('2024-01-15'),
        timeIn: new Date('2024-01-15T08:00:00Z'),
        timeOut: new Date('2024-01-15T16:00:00Z'),
        status: AttendanceStatus.PRESENT,
        remarks: null,
        student: {
          id: 'student-1',
          studentNumber: '123456789012',
          firstName: 'Juan',
          lastName: 'Dela Cruz',
          middleName: 'Santos',
          gradeLevel: 'Grade 7',
          section: 'A'
        },
        teacher: {
          firstName: 'Maria',
          lastName: 'Garcia',
          employeeNumber: 'T001'
        }
      },
      {
        id: 'attendance-2',
        date: new Date('2024-01-15'),
        timeIn: null,
        timeOut: null,
        status: AttendanceStatus.ABSENT,
        remarks: 'Sick',
        student: {
          id: 'student-2',
          studentNumber: '123456789013',
          firstName: 'Maria',
          lastName: 'Santos',
          middleName: 'Cruz',
          gradeLevel: 'Grade 7',
          section: 'A'
        },
        teacher: {
          firstName: 'Maria',
          lastName: 'Garcia',
          employeeNumber: 'T001'
        }
      }
    ]

    const mockTeacher = {
      id: 'teacher-123',
      firstName: 'Maria',
      lastName: 'Garcia',
      employeeNumber: 'T001'
    }

    beforeEach(() => {
      mockPrisma.attendance.findMany.mockResolvedValue(mockAttendanceData as any)
      mockPrisma.teacher.findFirst.mockResolvedValue(mockTeacher as any)
    })

    it('should generate SF2 data successfully', async () => {
      const result = await sf2Generator.generateSF2Data(mockParameters)

      expect(result).toMatchObject({
        schoolInfo: {
          name: 'Tanauan School of Arts and Trade',
          division: 'Batangas',
          region: 'Region IV-A (CALABARZON)'
        },
        reportDate: new Date('2024-01-15T00:00:00Z'),
        gradeSection: 'Grade 7 - A',
        classAdviser: {
          name: 'Maria Garcia',
          employeeNumber: 'T001'
        }
      })

      expect(result.attendanceSummary).toMatchObject({
        enrollment: { male: 1, female: 1, total: 2 },
        present: { male: 1, female: 0, total: 1 },
        absent: { male: 0, female: 1, total: 1 },
        late: { male: 0, female: 0, total: 0 },
        excused: { male: 0, female: 0, total: 0 }
      })

      expect(result.detailedAttendance).toHaveLength(2)
      expect(result.detailedAttendance[0]).toMatchObject({
        studentNumber: '123456789012',
        firstName: 'Juan',
        lastName: 'Dela Cruz',
        status: AttendanceStatus.PRESENT
      })
    })

    it('should throw error for multi-day date range', async () => {
      const invalidParameters = {
        ...mockParameters,
        dateRange: {
          start: new Date('2024-01-15T00:00:00Z'),
          end: new Date('2024-01-16T23:59:59Z')
        }
      }

      await expect(sf2Generator.generateSF2Data(invalidParameters))
        .rejects.toThrow('SF2 reports must be for a single day')
    })

    it('should handle empty attendance data', async () => {
      mockPrisma.attendance.findMany.mockResolvedValue([])

      const result = await sf2Generator.generateSF2Data(mockParameters)

      expect(result.attendanceSummary.enrollment.total).toBe(0)
      expect(result.detailedAttendance).toHaveLength(0)
    })
  })

  describe('validateSF2Data', () => {
    const validSF2Data = {
      schoolInfo: {
        name: 'Test School',
        division: 'Test Division',
        region: 'Test Region'
      },
      reportDate: new Date('2024-01-15'),
      gradeSection: 'Grade 7-A',
      classAdviser: {
        name: 'Test Teacher',
        employeeNumber: 'T001'
      },
      attendanceSummary: {
        enrollment: { male: 15, female: 10, total: 25 },
        present: { male: 14, female: 9, total: 23 },
        absent: { male: 1, female: 1, total: 2 },
        late: { male: 0, female: 0, total: 0 },
        excused: { male: 0, female: 0, total: 0 }
      },
      detailedAttendance: [
        {
          studentNumber: '123456789012',
          lastName: 'Dela Cruz',
          firstName: 'Juan',
          gender: 'M' as const,
          gradeLevel: 'Grade 7',
          section: 'A',
          status: AttendanceStatus.PRESENT
        }
      ],
      signatures: {
        classAdviser: { name: 'Test Teacher', date: new Date() },
        principal: { name: 'Test Principal', date: new Date() }
      }
    }

    it('should validate correct SF2 data', async () => {
      const result = await sf2Generator.validateSF2Data(validSF2Data)

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.complianceScore).toBeGreaterThan(80)
    })

    it('should detect missing required fields', async () => {
      const invalidData = {
        ...validSF2Data,
        schoolInfo: {
          ...validSF2Data.schoolInfo,
          name: ''
        },
        classAdviser: {
          name: ''
        }
      }

      const result = await sf2Generator.validateSF2Data(invalidData)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('School name is required')
      expect(result.errors).toContain('Class adviser name is required')
    })

    it('should detect attendance total mismatches', async () => {
      const invalidData = {
        ...validSF2Data,
        attendanceSummary: {
          enrollment: { male: 15, female: 10, total: 30 }, // Wrong total
          present: { male: 14, female: 9, total: 23 },
          absent: { male: 1, female: 1, total: 2 },
          late: { male: 0, female: 0, total: 0 },
          excused: { male: 0, female: 0, total: 0 }
        }
      }

      const result = await sf2Generator.validateSF2Data(invalidData)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Enrollment totals do not match male + female counts')
    })

    it('should validate detailed attendance records', async () => {
      const invalidData = {
        ...validSF2Data,
        detailedAttendance: [
          {
            studentNumber: '', // Missing student number
            lastName: '',      // Missing last name
            firstName: 'Juan',
            gender: 'X' as any, // Invalid gender
            gradeLevel: '',     // Missing grade level
            section: '',        // Missing section
            status: AttendanceStatus.PRESENT
          }
        ]
      }

      const result = await sf2Generator.validateSF2Data(invalidData)

      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.errors.some(error => error.includes('Student number is required'))).toBe(true)
      expect(result.errors.some(error => error.includes('Complete name is required'))).toBe(true)
      expect(result.errors.some(error => error.includes('Valid gender (M/F) is required'))).toBe(true)
    })
  })

  describe('generateSF2Template', () => {
    it('should generate valid SF2 template configuration', () => {
      const template = sf2Generator.generateSF2Template()

      expect(template).toHaveProperty('layout')
      expect(template).toHaveProperty('sections')
      expect(template).toHaveProperty('styling')

      expect(template.layout).toMatchObject({
        orientation: 'portrait',
        pageSize: 'A4'
      })

      expect(template.sections).toHaveProperty('header')
      expect(template.sections).toHaveProperty('schoolInfo')
      expect(template.sections).toHaveProperty('attendanceSummary')
      expect(template.sections).toHaveProperty('detailedAttendance')
      expect(template.sections).toHaveProperty('signatures')
    })
  })

  describe('private helper methods', () => {
    it('should format grade section correctly', () => {
      // Test the formatGradeSection method indirectly through generateSF2Data
      const parameters = {
        ...mockParameters,
        filters: {
          ...mockParameters.filters,
          grades: ['Grade 7', 'Grade 8'],
          sections: ['A', 'B']
        }
      }

      mockPrisma.attendance.findMany.mockResolvedValue([])
      mockPrisma.teacher.findFirst.mockResolvedValue(null)

      return sf2Generator.generateSF2Data(parameters).then(result => {
        expect(result.gradeSection).toBe('Grade 7, Grade 8 - A, B')
      })
    })

    it('should infer gender correctly', async () => {
      const maleAttendance = [{
        ...mockAttendanceData[0],
        student: {
          ...mockAttendanceData[0].student,
          firstName: 'John'
        }
      }]

      const femaleAttendance = [{
        ...mockAttendanceData[0],
        student: {
          ...mockAttendanceData[0].student,
          firstName: 'Maria'
        }
      }]

      mockPrisma.attendance.findMany.mockResolvedValueOnce(maleAttendance as any)
      mockPrisma.teacher.findFirst.mockResolvedValue(mockTeacher as any)

      const maleResult = await sf2Generator.generateSF2Data(mockParameters)
      expect(maleResult.detailedAttendance[0].gender).toBe('M')

      mockPrisma.attendance.findMany.mockResolvedValueOnce(femaleAttendance as any)
      
      const femaleResult = await sf2Generator.generateSF2Data(mockParameters)
      expect(femaleResult.detailedAttendance[0].gender).toBe('F')
    })
  })
})
