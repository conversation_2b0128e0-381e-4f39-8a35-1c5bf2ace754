import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  updateAttendanceSchema,
  attendanceIdSchema,
  validateTimeRange,
  type UpdateAttendanceInput,
  type AttendanceIdInput
} from '@/lib/validations/attendance'
import {
  attendanceStatusUtils,
  transformUtils
} from '@/lib/utils/attendance'
import { smsUtils } from '@/lib/utils/sms'

/**
 * GET /api/attendance/[id]
 * Get a specific attendance record
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-get', 100, 60 * 1000)
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Validate attendance ID parameter
    const paramValidation = attendanceIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid attendance ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    const { id: attendanceId }: AttendanceIdInput = paramValidation.data

    // Get attendance record
    const attendance = await prisma.attendance.findUnique({
      where: { id: attendanceId },
      include: {
        student: {
          select: {
            id: true,
            studentNumber: true,
            firstName: true,
            lastName: true,
            middleName: true,
            gradeLevel: true,
            section: true,
            status: true
          }
        },
        teacher: {
          select: {
            id: true,
            employeeNumber: true,
            firstName: true,
            lastName: true,
            middleName: true
          }
        },
        smsLogs: {
          select: {
            id: true,
            status: true,
            sentAt: true,
            deliveredAt: true
          }
        }
      }
    })

    if (!attendance) {
      return NextResponse.json(
        {
          success: false,
          error: 'Attendance record not found'
        },
        { status: 404 }
      )
    }

    // Transform response data
    const transformedAttendance = {
      ...transformUtils.transformAttendanceForResponse(attendance),
      smsNotifications: attendance.smsLogs.map(sms => ({
        id: sms.id,
        status: sms.status,
        sentAt: sms.sentAt,
        deliveredAt: sms.deliveredAt
      }))
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'attendance',
      resourceId: attendanceId,
      details: {
        studentId: attendance.studentId,
        studentName: `${attendance.student.firstName} ${attendance.student.lastName}`,
        date: attendance.date.toISOString()
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedAttendance
    })

  } catch (error) {
    console.error('GET /api/attendance/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/attendance/[id]
 * Update an attendance record
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-update', 20, 60 * 1000) // 20 updates per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many update requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (ADMIN, TEACHER, and STAFF can update attendance)
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Validate attendance ID parameter
    const paramValidation = attendanceIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid attendance ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    const { id: attendanceId }: AttendanceIdInput = paramValidation.data

    // Parse and validate request body
    const body = await request.json()
    const validationResult = updateAttendanceSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid update data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const updateData: UpdateAttendanceInput = validationResult.data

    // Validate time range if both timeIn and timeOut are provided
    if (updateData.timeIn && updateData.timeOut) {
      const timeValidation = validateTimeRange(updateData.timeIn, updateData.timeOut)
      if (!timeValidation.valid) {
        return NextResponse.json(
          {
            success: false,
            error: timeValidation.error
          },
          { status: 400 }
        )
      }
    }

    // Get existing attendance record
    const existingAttendance = await prisma.attendance.findUnique({
      where: { id: attendanceId },
      include: {
        student: {
          select: {
            id: true,
            studentNumber: true,
            firstName: true,
            lastName: true,
            status: true
          }
        }
      }
    })

    if (!existingAttendance) {
      return NextResponse.json(
        {
          success: false,
          error: 'Attendance record not found'
        },
        { status: 404 }
      )
    }

    // Check if user can update this record
    // Teachers can only update records they created, admins and staff can update any
    if (user.role === 'TEACHER' && existingAttendance.teacherId !== user.teacherProfileId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Insufficient permissions to update this record'
        },
        { status: 403 }
      )
    }

    // Prepare update data
    const updateFields: any = {}
    if (updateData.status !== undefined) updateFields.status = updateData.status
    if (updateData.timeIn !== undefined) updateFields.timeIn = updateData.timeIn
    if (updateData.timeOut !== undefined) updateFields.timeOut = updateData.timeOut
    if (updateData.remarks !== undefined) {
      updateFields.remarks = updateData.remarks ? 
        `${existingAttendance.remarks || ''} | Updated: ${updateData.remarks}`.trim() :
        existingAttendance.remarks
    }

    // Update attendance record in transaction
    const updatedAttendance = await prisma.$transaction(async (tx) => {
      const updated = await tx.attendance.update({
        where: { id: attendanceId },
        data: updateFields,
        include: {
          student: {
            select: {
              id: true,
              studentNumber: true,
              firstName: true,
              lastName: true,
              middleName: true,
              gradeLevel: true,
              section: true,
              status: true
            }
          },
          teacher: {
            select: {
              id: true,
              employeeNumber: true,
              firstName: true,
              lastName: true,
              middleName: true
            }
          }
        }
      })

      return updated
    })

    // Send SMS notification if status changed
    if (updateData.status && updateData.status !== existingAttendance.status) {
      smsUtils.sendAttendanceNotification(
        existingAttendance.studentId,
        attendanceId,
        updateData.status,
        updateData.timeIn || existingAttendance.timeIn
      ).catch(error => {
        console.warn('SMS notification failed:', error)
      })
    }

    // Transform response data
    const transformedAttendance = transformUtils.transformAttendanceForResponse(updatedAttendance)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'UPDATE',
      resource: 'attendance',
      resourceId: attendanceId,
      details: {
        studentId: existingAttendance.studentId,
        studentName: `${existingAttendance.student.firstName} ${existingAttendance.student.lastName}`,
        changes: updateFields,
        previousStatus: existingAttendance.status,
        newStatus: updateData.status || existingAttendance.status
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedAttendance,
      message: 'Attendance record updated successfully'
    })

  } catch (error) {
    console.error('PUT /api/attendance/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/attendance/[id]
 * Delete an attendance record
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-delete', 10, 60 * 1000) // 10 deletes per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many delete requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and STAFF can delete attendance records)
    if (!['ADMIN', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions to delete attendance records' },
        { status: 403 }
      )
    }

    // Validate attendance ID parameter
    const paramValidation = attendanceIdSchema.safeParse({ id: params.id })
    if (!paramValidation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid attendance ID format',
          details: paramValidation.error.errors
        },
        { status: 400 }
      )
    }

    const { id: attendanceId }: AttendanceIdInput = paramValidation.data

    // Get existing attendance record for audit trail
    const existingAttendance = await prisma.attendance.findUnique({
      where: { id: attendanceId },
      include: {
        student: {
          select: {
            id: true,
            studentNumber: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    if (!existingAttendance) {
      return NextResponse.json(
        {
          success: false,
          error: 'Attendance record not found'
        },
        { status: 404 }
      )
    }

    // Delete attendance record in transaction
    await prisma.$transaction(async (tx) => {
      // Delete related SMS logs first
      await tx.sMSLog.deleteMany({
        where: { attendanceId }
      })

      // Delete the attendance record
      await tx.attendance.delete({
        where: { id: attendanceId }
      })
    })

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'DELETE',
      resource: 'attendance',
      resourceId: attendanceId,
      details: {
        studentId: existingAttendance.studentId,
        studentName: `${existingAttendance.student.firstName} ${existingAttendance.student.lastName}`,
        studentNumber: existingAttendance.student.studentNumber,
        date: existingAttendance.date.toISOString(),
        status: existingAttendance.status,
        timeIn: existingAttendance.timeIn?.toISOString(),
        timeOut: existingAttendance.timeOut?.toISOString()
      }
    }, request)

    return NextResponse.json({
      success: true,
      message: 'Attendance record deleted successfully'
    })

  } catch (error) {
    console.error('DELETE /api/attendance/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST /api/attendance/scan or /api/attendance/manual to create attendance records.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use PUT to update attendance records.' },
    { status: 405 }
  )
}
