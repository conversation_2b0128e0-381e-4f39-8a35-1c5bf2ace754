import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import { NextRequest } from 'next/server'
import { POST as SF2Post, GET as SF2Get } from '@/app/api/reports/sf2/route'
import { POST as SF4Post, GET as SF4Get } from '@/app/api/reports/sf4/route'
import { GET as ListGet, DELETE as ListDelete } from '@/app/api/reports/list/route'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { ReportService } from '@/lib/services/report-service'

// Mock dependencies
jest.mock('@/lib/auth')
jest.mock('@/lib/rate-limit')
jest.mock('@/lib/services/report-service')
jest.mock('@/lib/audit')

const mockGetUserFromToken = getUserFromToken as jest.MockedFunction<typeof getUserFromToken>
const mockRateLimit = rateLimit as jest.MockedFunction<typeof rateLimit>
const mockReportService = {
  generateReport: jest.fn(),
  getReport: jest.fn(),
  listReports: jest.fn(),
  deleteReport: jest.fn()
}

// Mock ReportService.getInstance
jest.mocked(ReportService.getInstance).mockReturnValue(mockReportService as any)

describe('Reports API', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    role: 'TEACHER'
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Default successful auth and rate limiting
    mockGetUserFromToken.mockResolvedValue(mockUser as any)
    mockRateLimit.mockResolvedValue({ success: true } as any)
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('SF2 Reports API', () => {
    describe('POST /api/reports/sf2', () => {
      const validSF2Request = {
        name: 'Daily Attendance Report',
        description: 'Grade 7-A attendance for January 15, 2024',
        parameters: {
          dateRange: {
            start: '2024-01-15T00:00:00Z',
            end: '2024-01-15T23:59:59Z'
          },
          filters: {
            grades: ['Grade 7'],
            sections: ['A']
          },
          format: 'pdf',
          includeCharts: false,
          includeStatistics: true,
          includeSignatures: true
        },
        delivery: {
          email: {
            enabled: true,
            recipients: ['<EMAIL>'],
            subject: 'Daily Attendance Report'
          },
          print: {
            enabled: false,
            copies: 1
          }
        }
      }

      it('should generate SF2 report successfully', async () => {
        const mockGenerationResult = {
          success: true,
          reportId: 'report-123',
          fileUrl: 'reports/sf2-report.pdf',
          complianceIssues: []
        }

        mockReportService.generateReport.mockResolvedValue(mockGenerationResult)

        const request = new NextRequest('http://localhost/api/reports/sf2', {
          method: 'POST',
          body: JSON.stringify(validSF2Request),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await SF2Post(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.reportId).toBe('report-123')
        expect(data.fileUrl).toBe('reports/sf2-report.pdf')
        expect(mockReportService.generateReport).toHaveBeenCalledWith(
          expect.objectContaining({
            name: validSF2Request.name,
            type: 'SF2',
            userId: mockUser.id
          })
        )
      })

      it('should reject multi-day date range', async () => {
        const invalidRequest = {
          ...validSF2Request,
          parameters: {
            ...validSF2Request.parameters,
            dateRange: {
              start: '2024-01-15T00:00:00Z',
              end: '2024-01-16T23:59:59Z' // Next day
            }
          }
        }

        const request = new NextRequest('http://localhost/api/reports/sf2', {
          method: 'POST',
          body: JSON.stringify(invalidRequest),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await SF2Post(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.success).toBe(false)
        expect(data.error).toContain('single day')
      })

      it('should handle unauthorized access', async () => {
        mockGetUserFromToken.mockResolvedValue(null)

        const request = new NextRequest('http://localhost/api/reports/sf2', {
          method: 'POST',
          body: JSON.stringify(validSF2Request),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await SF2Post(request)
        const data = await response.json()

        expect(response.status).toBe(401)
        expect(data.success).toBe(false)
        expect(data.error).toBe('Unauthorized')
      })

      it('should handle rate limiting', async () => {
        mockRateLimit.mockResolvedValue({
          success: false,
          retryAfter: 900
        } as any)

        const request = new NextRequest('http://localhost/api/reports/sf2', {
          method: 'POST',
          body: JSON.stringify(validSF2Request),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await SF2Post(request)
        const data = await response.json()

        expect(response.status).toBe(429)
        expect(data.success).toBe(false)
        expect(data.error).toBe('Rate limit exceeded')
        expect(data.retryAfter).toBe(900)
      })

      it('should handle validation errors', async () => {
        const invalidRequest = {
          name: '', // Empty name
          parameters: {
            dateRange: {
              start: 'invalid-date',
              end: '2024-01-15T23:59:59Z'
            },
            filters: {},
            format: 'invalid-format'
          }
        }

        const request = new NextRequest('http://localhost/api/reports/sf2', {
          method: 'POST',
          body: JSON.stringify(invalidRequest),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await SF2Post(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.success).toBe(false)
        expect(data.error).toBe('Validation error')
        expect(data.details).toBeDefined()
      })
    })

    describe('GET /api/reports/sf2', () => {
      it('should return SF2 templates and validation info', async () => {
        const request = new NextRequest('http://localhost/api/reports/sf2')
        const response = await SF2Get(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.templates).toBeDefined()
        expect(data.defaultTemplate).toBeDefined()
        expect(data.complianceRequirements).toBeDefined()
        expect(data.validationRules).toBeDefined()
      })
    })
  })

  describe('SF4 Reports API', () => {
    describe('POST /api/reports/sf4', () => {
      const validSF4Request = {
        name: 'School Register Report',
        description: 'Complete student enrollment register',
        parameters: {
          dateRange: {
            start: '2024-01-01T00:00:00Z',
            end: '2024-12-31T23:59:59Z'
          },
          filters: {
            grades: ['Grade 7'],
            sections: ['A', 'B']
          },
          format: 'excel',
          includeCharts: false,
          includeStatistics: true,
          includeSignatures: true
        }
      }

      it('should generate SF4 report successfully', async () => {
        // Update user role to admin for SF4 access
        mockGetUserFromToken.mockResolvedValue({
          ...mockUser,
          role: 'ADMIN'
        } as any)

        const mockGenerationResult = {
          success: true,
          reportId: 'report-456',
          fileUrl: 'reports/sf4-report.xlsx',
          complianceIssues: []
        }

        mockReportService.generateReport.mockResolvedValue(mockGenerationResult)

        const request = new NextRequest('http://localhost/api/reports/sf4', {
          method: 'POST',
          body: JSON.stringify(validSF4Request),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await SF4Post(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.reportId).toBe('report-456')
      })

      it('should reject insufficient permissions', async () => {
        // Keep user as TEACHER (insufficient for SF4)
        const request = new NextRequest('http://localhost/api/reports/sf4', {
          method: 'POST',
          body: JSON.stringify(validSF4Request),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await SF4Post(request)
        const data = await response.json()

        expect(response.status).toBe(403)
        expect(data.success).toBe(false)
        expect(data.error).toContain('Insufficient permissions')
      })
    })
  })

  describe('Reports List API', () => {
    describe('GET /api/reports/list', () => {
      it('should list reports with pagination', async () => {
        const mockReports = [
          {
            id: 'report-1',
            name: 'Report 1',
            type: 'SF2',
            status: 'COMPLETED',
            generatedAt: new Date(),
            downloadCount: 5
          },
          {
            id: 'report-2',
            name: 'Report 2',
            type: 'SF4',
            status: 'COMPLETED',
            generatedAt: new Date(),
            downloadCount: 2
          }
        ]

        mockReportService.listReports.mockResolvedValue({
          reports: mockReports,
          total: 2
        })

        const request = new NextRequest('http://localhost/api/reports/list?page=1&limit=10&type=SF2')
        const response = await ListGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.reports).toHaveLength(2)
        expect(data.pagination).toMatchObject({
          page: 1,
          limit: 10,
          total: 2
        })
      })

      it('should handle search and filtering', async () => {
        mockReportService.listReports.mockResolvedValue({
          reports: [],
          total: 0
        })

        const request = new NextRequest('http://localhost/api/reports/list?search=attendance&status=COMPLETED&dateFrom=2024-01-01')
        const response = await ListGet(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(mockReportService.listReports).toHaveBeenCalledWith(
          mockUser.id,
          expect.objectContaining({
            dateFrom: expect.any(Date)
          })
        )
      })
    })

    describe('DELETE /api/reports/list', () => {
      it('should bulk delete reports', async () => {
        mockReportService.deleteReport
          .mockResolvedValueOnce(true)
          .mockResolvedValueOnce(true)
          .mockResolvedValueOnce(false)

        const request = new NextRequest('http://localhost/api/reports/list', {
          method: 'DELETE',
          body: JSON.stringify({
            reportIds: ['report-1', 'report-2', 'report-3']
          }),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await ListDelete(request)
        const data = await response.json()

        expect(response.status).toBe(200)
        expect(data.success).toBe(true)
        expect(data.deleted).toBe(2)
        expect(data.failed).toBe(1)
      })

      it('should validate report IDs array', async () => {
        const request = new NextRequest('http://localhost/api/reports/list', {
          method: 'DELETE',
          body: JSON.stringify({
            reportIds: 'invalid'
          }),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await ListDelete(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.success).toBe(false)
        expect(data.error).toContain('Report IDs array is required')
      })

      it('should limit bulk delete size', async () => {
        const largeArray = Array.from({ length: 51 }, (_, i) => `report-${i}`)

        const request = new NextRequest('http://localhost/api/reports/list', {
          method: 'DELETE',
          body: JSON.stringify({
            reportIds: largeArray
          }),
          headers: { 'Content-Type': 'application/json' }
        })

        const response = await ListDelete(request)
        const data = await response.json()

        expect(response.status).toBe(400)
        expect(data.success).toBe(false)
        expect(data.error).toContain('Cannot delete more than 50 reports')
      })
    })
  })
})
