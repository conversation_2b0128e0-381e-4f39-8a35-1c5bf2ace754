import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  attendanceAnalyticsSchema,
  type AttendanceAnalyticsInput
} from '@/lib/validations/attendance'
import {
  attendanceDbUtils,
  attendanceAnalyticsUtils,
  attendanceStatusUtils
} from '@/lib/utils/attendance'

/**
 * GET /api/attendance/analytics
 * Get comprehensive attendance analytics and insights
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-analytics', 30, 60 * 1000) // 30 requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const validationResult = attendanceAnalyticsSchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const query: AttendanceAnalyticsInput = validationResult.data

    // Get overall statistics for the period
    const overallStats = await attendanceDbUtils.getAttendanceStats(
      query.startDate,
      query.endDate,
      {
        gradeLevel: query.gradeLevel,
        section: query.section
      }
    )

    // Initialize analytics data
    const analyticsData: any = {
      period: {
        startDate: query.startDate.toISOString().split('T')[0],
        endDate: query.endDate.toISOString().split('T')[0],
        totalDays: Math.ceil((query.endDate.getTime() - query.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1
      },
      summary: overallStats,
      trends: [],
      gradeBreakdown: [],
      atRiskStudents: [],
      insights: []
    }

    // Get daily trends if requested
    if (query.metrics.includes('trends') || query.groupBy === 'day') {
      analyticsData.trends = await attendanceAnalyticsUtils.getDailyTrends(
        query.startDate,
        query.endDate,
        {
          gradeLevel: query.gradeLevel,
          section: query.section
        }
      )
    }

    // Get grade/section breakdown if requested
    if (query.metrics.includes('attendance_rate') || query.groupBy === 'grade' || query.groupBy === 'section') {
      analyticsData.gradeBreakdown = await attendanceAnalyticsUtils.getAttendanceByGradeSection(
        query.startDate,
        query.endDate
      )

      // Filter by grade/section if specified
      if (query.gradeLevel || query.section) {
        analyticsData.gradeBreakdown = analyticsData.gradeBreakdown.filter((item: any) => {
          return (!query.gradeLevel || item.gradeLevel === query.gradeLevel) &&
                 (!query.section || item.section === query.section)
        })
      }
    }

    // Get at-risk students
    analyticsData.atRiskStudents = await attendanceAnalyticsUtils.getAtRiskStudents(
      query.startDate,
      query.endDate,
      75 // 75% attendance threshold
    )

    // Filter at-risk students by grade/section if specified
    if (query.gradeLevel || query.section) {
      analyticsData.atRiskStudents = analyticsData.atRiskStudents.filter((student: any) => {
        return (!query.gradeLevel || student.gradeLevel === query.gradeLevel) &&
               (!query.section || student.section === query.section)
      })
    }

    // Get peak attendance hours
    const peakHours = await prisma.$queryRaw`
      SELECT 
        CAST(strftime('%H', timeIn) AS INTEGER) as hour,
        COUNT(*) as count
      FROM attendances
      WHERE date >= ${query.startDate} 
        AND date <= ${query.endDate}
        AND timeIn IS NOT NULL
        ${query.gradeLevel ? `AND studentId IN (SELECT id FROM students WHERE gradeLevel = ${query.gradeLevel})` : ''}
        ${query.section ? `AND studentId IN (SELECT id FROM students WHERE section = ${query.section})` : ''}
      GROUP BY hour
      ORDER BY count DESC
      LIMIT 5
    ` as any[]

    analyticsData.peakHours = peakHours.map((item: any) => ({
      hour: `${item.hour.toString().padStart(2, '0')}:00`,
      count: Number(item.count),
      percentage: overallStats.total > 0 ? (Number(item.count) / overallStats.total) * 100 : 0
    }))

    // Get attendance patterns by day of week
    const weeklyPatterns = await prisma.$queryRaw`
      SELECT 
        CASE CAST(strftime('%w', date) AS INTEGER)
          WHEN 0 THEN 'Sunday'
          WHEN 1 THEN 'Monday'
          WHEN 2 THEN 'Tuesday'
          WHEN 3 THEN 'Wednesday'
          WHEN 4 THEN 'Thursday'
          WHEN 5 THEN 'Friday'
          WHEN 6 THEN 'Saturday'
        END as dayOfWeek,
        status,
        COUNT(*) as count
      FROM attendances
      WHERE date >= ${query.startDate} 
        AND date <= ${query.endDate}
        ${query.gradeLevel ? `AND studentId IN (SELECT id FROM students WHERE gradeLevel = ${query.gradeLevel})` : ''}
        ${query.section ? `AND studentId IN (SELECT id FROM students WHERE section = ${query.section})` : ''}
      GROUP BY dayOfWeek, status
      ORDER BY 
        CASE dayOfWeek
          WHEN 'Monday' THEN 1
          WHEN 'Tuesday' THEN 2
          WHEN 'Wednesday' THEN 3
          WHEN 'Thursday' THEN 4
          WHEN 'Friday' THEN 5
          WHEN 'Saturday' THEN 6
          WHEN 'Sunday' THEN 7
        END
    ` as any[]

    // Process weekly patterns
    const weeklyMap = new Map()
    weeklyPatterns.forEach((item: any) => {
      if (!weeklyMap.has(item.dayOfWeek)) {
        weeklyMap.set(item.dayOfWeek, {
          dayOfWeek: item.dayOfWeek,
          present: 0,
          absent: 0,
          late: 0,
          excused: 0,
          total: 0
        })
      }
      
      const dayData = weeklyMap.get(item.dayOfWeek)
      dayData.total += Number(item.count)
      
      switch (item.status) {
        case 'PRESENT':
          dayData.present = Number(item.count)
          break
        case 'ABSENT':
          dayData.absent = Number(item.count)
          break
        case 'LATE':
          dayData.late = Number(item.count)
          break
        case 'EXCUSED':
          dayData.excused = Number(item.count)
          break
      }
    })

    analyticsData.weeklyPatterns = Array.from(weeklyMap.values()).map((day: any) => ({
      ...day,
      attendanceRate: day.total > 0 ? ((day.present + day.late + day.excused) / day.total) * 100 : 0
    }))

    // Generate insights based on the data
    const insights = []

    // Attendance rate insight
    if (overallStats.attendanceRate < 85) {
      insights.push({
        type: 'warning',
        title: 'Low Attendance Rate',
        description: `Overall attendance rate is ${overallStats.attendanceRate.toFixed(1)}%, which is below the recommended 85% threshold.`,
        recommendation: 'Consider implementing attendance improvement strategies and parent engagement programs.'
      })
    } else if (overallStats.attendanceRate > 95) {
      insights.push({
        type: 'success',
        title: 'Excellent Attendance',
        description: `Outstanding attendance rate of ${overallStats.attendanceRate.toFixed(1)}%!`,
        recommendation: 'Continue current attendance policies and recognition programs.'
      })
    }

    // At-risk students insight
    if (analyticsData.atRiskStudents.length > 0) {
      const highRiskCount = analyticsData.atRiskStudents.filter((s: any) => s.riskLevel === 'high').length
      insights.push({
        type: 'alert',
        title: 'Students at Risk',
        description: `${analyticsData.atRiskStudents.length} students have attendance below 75%, with ${highRiskCount} at high risk.`,
        recommendation: 'Implement targeted interventions for at-risk students and engage with parents.'
      })
    }

    // Weekly pattern insight
    const mondayData = analyticsData.weeklyPatterns.find((d: any) => d.dayOfWeek === 'Monday')
    const fridayData = analyticsData.weeklyPatterns.find((d: any) => d.dayOfWeek === 'Friday')
    
    if (mondayData && fridayData) {
      if (mondayData.attendanceRate < fridayData.attendanceRate - 10) {
        insights.push({
          type: 'info',
          title: 'Monday Attendance Pattern',
          description: `Monday attendance (${mondayData.attendanceRate.toFixed(1)}%) is significantly lower than Friday (${fridayData.attendanceRate.toFixed(1)}%).`,
          recommendation: 'Consider Monday motivation programs or investigate weekend-related attendance issues.'
        })
      }
    }

    analyticsData.insights = insights

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'VIEW',
      resource: 'attendance',
      details: {
        endpoint: 'analytics',
        dateRange: {
          start: query.startDate.toISOString(),
          end: query.endDate.toISOString()
        },
        filters: {
          gradeLevel: query.gradeLevel,
          section: query.section,
          groupBy: query.groupBy,
          metrics: query.metrics
        },
        resultSummary: {
          totalRecords: overallStats.total,
          attendanceRate: overallStats.attendanceRate,
          atRiskStudents: analyticsData.atRiskStudents.length
        }
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: analyticsData,
      meta: {
        generatedAt: new Date().toISOString(),
        filters: {
          startDate: query.startDate.toISOString().split('T')[0],
          endDate: query.endDate.toISOString().split('T')[0],
          gradeLevel: query.gradeLevel,
          section: query.section,
          groupBy: query.groupBy,
          metrics: query.metrics,
          includeWeekends: query.includeWeekends
        }
      }
    })

  } catch (error) {
    console.error('GET /api/attendance/analytics error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve attendance analytics.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve attendance analytics.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve attendance analytics.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve attendance analytics.' },
    { status: 405 }
  )
}
