// This is your Prisma schema file for QR-Code Based Student Attendance and Monitoring System
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Enums for better type safety
enum StudentStatus {
  ACTIVE
  INACTIVE
  GRADUATED
  TRANSFERRED
  DROPPED
}

enum TeacherStatus {
  ACTIVE
  INACTIVE
  ON_LEAVE
  TERMINATED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum SMSStatus {
  PENDING
  QUEUED
  SENT
  DELIVERED
  FAILED
  CANCELLED
}

enum SMSType {
  ATTENDANCE
  ANNOUNCEMENT
  REMINDER
  ALERT
  CUSTOM
}

enum TemplateCategory {
  ATTENDANCE
  ANNOUNCEMENT
  REMINDER
  ALERT
  GENERAL
}

enum SMSPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum UserRole {
  ADMIN
  TEACHER
  STAFF
}

enum AuditAction {
  LOGIN
  LOGOUT
  CREATE
  UPDATE
  DELETE
  VIEW
  EXPORT
  IMPORT
}

// Users table for authentication
model User {
  id                   String    @id @default(cuid())
  username             String    @unique
  email                String    @unique
  passwordHash         String
  firstName            String
  lastName             String
  middleName           String?
  role                 UserRole  @default(STAFF)
  isActive             Boolean   @default(true)
  lastLoginAt          DateTime?
  loginAttempts        Int       @default(0)
  lockedUntil          DateTime?
  passwordResetToken   String?
  passwordResetExpires DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  // Relations
  auditLogs        AuditLog[]
  teacherProfile   Teacher?   @relation(fields: [teacherProfileId], references: [id])
  teacherProfileId String?

  // SMS Relations
  smsLogs      SMSLog[]
  smsTemplates SMSTemplate[]
  smsBlacklist SMSBlacklist[]
  smsSettings  SMSSetting[]

  @@index([username])
  @@index([email])
  @@index([role])
  @@index([isActive])
  @@map("users")
}

// Audit Log table for tracking user actions
model AuditLog {
  id         String      @id @default(cuid())
  userId     String?
  action     AuditAction
  resource   String // e.g., "student", "attendance", "user"
  resourceId String? // ID of the affected resource
  details    String? // JSON string with additional details
  ipAddress  String?
  userAgent  String?
  timestamp  DateTime    @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@map("audit_logs")
}

// Students table
model Student {
  id              String        @id @default(cuid())
  studentNumber   String        @unique
  firstName       String
  lastName        String
  middleName      String?
  gradeLevel      String
  section         String
  guardianName    String
  guardianContact String
  address         String
  profilePhoto    String?
  dateEnrolled    DateTime      @default(now())
  status          StudentStatus @default(ACTIVE)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  attendances Attendance[]
  qrCodes     QRCode[]
  smsLogs     SMSLog[]

  @@index([studentNumber])
  @@index([gradeLevel, section])
  @@index([status])
  @@map("students")
}

// Teachers table
model Teacher {
  id              String        @id @default(cuid())
  employeeNumber  String        @unique
  firstName       String
  lastName        String
  middleName      String?
  contactNumber   String
  email           String        @unique
  subjectsHandled String // JSON string of subjects array
  advisorySection String?
  status          TeacherStatus @default(ACTIVE)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  attendances Attendance[]
  classes     Class[]
  user        User[] // One teacher can have multiple user accounts (if needed)

  @@index([employeeNumber])
  @@index([email])
  @@index([status])
  @@map("teachers")
}

// Attendance table
model Attendance {
  id        String           @id @default(cuid())
  studentId String
  teacherId String
  date      DateTime
  timeIn    DateTime?
  timeOut   DateTime?
  status    AttendanceStatus
  remarks   String?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relations
  student Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  teacher Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  smsLogs SMSLog[]

  @@unique([studentId, date]) // One attendance record per student per day
  @@index([studentId])
  @@index([teacherId])
  @@index([date])
  @@index([status])
  @@map("attendances")
}

// Classes table
model Class {
  id           String   @id @default(cuid())
  subjectName  String
  teacherId    String
  gradeLevel   String
  section      String
  scheduleTime String // JSON string for schedule details
  roomNumber   String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  teacher Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@index([teacherId])
  @@index([gradeLevel, section])
  @@index([subjectName])
  @@map("classes")
}

// SMS Logs table
model SMSLog {
  id              String      @id @default(cuid())
  recipientNumber String
  message         String
  status          SMSStatus   @default(PENDING)
  type            SMSType     @default(CUSTOM)
  priority        SMSPriority @default(NORMAL)
  sentAt          DateTime?
  deliveredAt     DateTime?
  failedAt        DateTime?
  scheduledFor    DateTime?
  retryCount      Int         @default(0)
  maxRetries      Int         @default(3)
  cost            Float? // Cost in PHP
  messageId       String? // Semaphore message ID
  errorMessage    String?
  studentId       String?
  attendanceId    String?
  templateId      String?
  batchId         String? // For batch operations
  userId          String? // User who sent the SMS
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @default(now()) @updatedAt

  // Relations
  student    Student?     @relation(fields: [studentId], references: [id], onDelete: SetNull)
  attendance Attendance?  @relation(fields: [attendanceId], references: [id], onDelete: SetNull)
  template   SMSTemplate? @relation(fields: [templateId], references: [id], onDelete: SetNull)
  user       User?        @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([recipientNumber])
  @@index([status])
  @@index([type])
  @@index([priority])
  @@index([studentId])
  @@index([attendanceId])
  @@index([templateId])
  @@index([batchId])
  @@index([userId])
  @@index([sentAt])
  @@index([scheduledFor])
  @@index([createdAt])
  @@map("sms_logs")
}

// SMS Templates table
model SMSTemplate {
  id          String           @id @default(cuid())
  name        String           @unique
  category    TemplateCategory @default(GENERAL)
  subject     String? // Optional subject for categorization
  content     String // Template content with variables like {studentName}
  variables   String // JSON array of available variables
  isActive    Boolean          @default(true)
  isSystem    Boolean          @default(false) // System templates cannot be deleted
  description String?
  createdBy   String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  smsLogs SMSLog[]
  creator User?    @relation(fields: [createdBy], references: [id], onDelete: SetNull)

  @@index([category])
  @@index([isActive])
  @@index([createdBy])
  @@map("sms_templates")
}

// SMS Blacklist table for opt-outs
model SMSBlacklist {
  id          String   @id @default(cuid())
  phoneNumber String   @unique
  reason      String? // Reason for blacklisting
  addedBy     String?
  createdAt   DateTime @default(now())

  // Relations
  addedByUser User? @relation(fields: [addedBy], references: [id], onDelete: SetNull)

  @@index([phoneNumber])
  @@map("sms_blacklist")
}

// SMS Cost Tracking table
model SMSCost {
  id            String   @id @default(cuid())
  date          DateTime // Daily cost tracking (using DateTime for SQLite compatibility)
  totalSent     Int      @default(0)
  totalCost     Float    @default(0.0) // Total cost in PHP
  avgCostPerSMS Float    @default(0.0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@unique([date])
  @@index([date])
  @@map("sms_costs")
}

// SMS Settings table
model SMSSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  isEncrypted Boolean  @default(false)
  updatedBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  updatedByUser User? @relation(fields: [updatedBy], references: [id], onDelete: SetNull)

  @@index([key])
  @@map("sms_settings")
}

// QR Codes table
model QRCode {
  id          String    @id @default(cuid())
  studentId   String
  qrData      String    @unique
  generatedAt DateTime  @default(now())
  expiresAt   DateTime?
  isActive    Boolean   @default(true)

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@index([studentId])
  @@index([qrData])
  @@index([isActive])
  @@index([expiresAt])
  @@map("qr_codes")
}

// System Settings table
model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  updatedAt   DateTime @updatedAt

  @@index([key])
  @@map("system_settings")
}
