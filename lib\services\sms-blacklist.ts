import { prisma } from '@/lib/db'
import { SemaphoreService } from './semaphore'
import { 
  SMSBlacklistEntry, 
  CreateBlacklistEntryInput,
  PhoneNumberValidation 
} from '@/lib/types/sms'

export class SMSBlacklistService {
  
  /**
   * Add phone number to blacklist
   */
  static async addToBlacklist(
    input: CreateBlacklistEntryInput,
    addedBy?: string
  ): Promise<SMSBlacklistEntry> {
    // Validate and format phone number
    const formattedNumber = this.formatPhoneNumber(input.phoneNumber)
    if (!formattedNumber) {
      throw new Error('Invalid phone number format')
    }

    // Check if already blacklisted
    const existing = await prisma.sMSBlacklist.findUnique({
      where: { phoneNumber: formattedNumber }
    })

    if (existing) {
      throw new Error('Phone number is already blacklisted')
    }

    // Add to blacklist
    const blacklistEntry = await prisma.sMSBlacklist.create({
      data: {
        phoneNumber: formattedNumber,
        reason: input.reason,
        addedBy
      }
    })

    return {
      id: blacklistEntry.id,
      phoneNumber: blacklistEntry.phoneNumber,
      reason: blacklistEntry.reason,
      addedBy: blacklistEntry.addedBy,
      createdAt: blacklistEntry.createdAt
    }
  }

  /**
   * Remove phone number from blacklist
   */
  static async removeFromBlacklist(phoneNumber: string): Promise<void> {
    const formattedNumber = this.formatPhoneNumber(phoneNumber)
    if (!formattedNumber) {
      throw new Error('Invalid phone number format')
    }

    const deleted = await prisma.sMSBlacklist.delete({
      where: { phoneNumber: formattedNumber }
    })

    if (!deleted) {
      throw new Error('Phone number not found in blacklist')
    }
  }

  /**
   * Check if phone number is blacklisted
   */
  static async isBlacklisted(phoneNumber: string): Promise<boolean> {
    const formattedNumber = this.formatPhoneNumber(phoneNumber)
    if (!formattedNumber) {
      return false
    }

    const entry = await prisma.sMSBlacklist.findUnique({
      where: { phoneNumber: formattedNumber }
    })

    return !!entry
  }

  /**
   * Get blacklist entry details
   */
  static async getBlacklistEntry(phoneNumber: string): Promise<SMSBlacklistEntry | null> {
    const formattedNumber = this.formatPhoneNumber(phoneNumber)
    if (!formattedNumber) {
      return null
    }

    const entry = await prisma.sMSBlacklist.findUnique({
      where: { phoneNumber: formattedNumber },
      include: {
        addedByUser: {
          select: {
            firstName: true,
            lastName: true,
            username: true
          }
        }
      }
    })

    if (!entry) {
      return null
    }

    return {
      id: entry.id,
      phoneNumber: entry.phoneNumber,
      reason: entry.reason,
      addedBy: entry.addedBy,
      createdAt: entry.createdAt
    }
  }

  /**
   * Get all blacklisted numbers with pagination
   */
  static async getBlacklistedNumbers(
    page = 1,
    limit = 50,
    search?: string
  ): Promise<{
    entries: SMSBlacklistEntry[]
    total: number
    page: number
    limit: number
    totalPages: number
  }> {
    const where: any = {}
    
    if (search) {
      where.OR = [
        { phoneNumber: { contains: search } },
        { reason: { contains: search } }
      ]
    }

    const [entries, total] = await Promise.all([
      prisma.sMSBlacklist.findMany({
        where,
        include: {
          addedByUser: {
            select: {
              firstName: true,
              lastName: true,
              username: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.sMSBlacklist.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return {
      entries: entries.map(entry => ({
        id: entry.id,
        phoneNumber: entry.phoneNumber,
        reason: entry.reason,
        addedBy: entry.addedBy,
        createdAt: entry.createdAt
      })),
      total,
      page,
      limit,
      totalPages
    }
  }

  /**
   * Bulk add numbers to blacklist
   */
  static async bulkAddToBlacklist(
    phoneNumbers: string[],
    reason?: string,
    addedBy?: string
  ): Promise<{
    success: number
    failed: number
    errors: Array<{ phoneNumber: string; error: string }>
  }> {
    let success = 0
    let failed = 0
    const errors: Array<{ phoneNumber: string; error: string }> = []

    for (const phoneNumber of phoneNumbers) {
      try {
        await this.addToBlacklist({ phoneNumber, reason }, addedBy)
        success++
      } catch (error) {
        failed++
        errors.push({
          phoneNumber,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return { success, failed, errors }
  }

  /**
   * Bulk remove numbers from blacklist
   */
  static async bulkRemoveFromBlacklist(
    phoneNumbers: string[]
  ): Promise<{
    success: number
    failed: number
    errors: Array<{ phoneNumber: string; error: string }>
  }> {
    let success = 0
    let failed = 0
    const errors: Array<{ phoneNumber: string; error: string }> = []

    for (const phoneNumber of phoneNumbers) {
      try {
        await this.removeFromBlacklist(phoneNumber)
        success++
      } catch (error) {
        failed++
        errors.push({
          phoneNumber,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return { success, failed, errors }
  }

  /**
   * Validate phone number and get details
   */
  static validatePhoneNumber(phoneNumber: string): PhoneNumberValidation {
    const cleaned = phoneNumber.replace(/\D/g, '')
    
    // Check Philippine number patterns
    let isValid = false
    let formatted: string | undefined
    let type: 'mobile' | 'landline' | 'unknown' = 'unknown'

    if (cleaned.match(/^63\d{10}$/)) {
      // Already in +63 format
      isValid = true
      formatted = cleaned
      type = 'mobile'
    } else if (cleaned.match(/^09\d{9}$/)) {
      // Convert 09XXXXXXXXX to 639XXXXXXXXX
      isValid = true
      formatted = '63' + cleaned.substring(1)
      type = 'mobile'
    } else if (cleaned.match(/^9\d{9}$/)) {
      // Convert 9XXXXXXXXX to 639XXXXXXXXX
      isValid = true
      formatted = '63' + cleaned
      type = 'mobile'
    }

    return {
      isValid,
      formatted,
      country: isValid ? 'Philippines' : undefined,
      type
    }
  }

  /**
   * Format phone number to standard format
   */
  private static formatPhoneNumber(phoneNumber: string): string | null {
    const validation = this.validatePhoneNumber(phoneNumber)
    return validation.formatted || null
  }

  /**
   * Import blacklist from CSV data
   */
  static async importFromCSV(
    csvData: string,
    addedBy?: string
  ): Promise<{
    success: number
    failed: number
    errors: Array<{ line: number; phoneNumber: string; error: string }>
  }> {
    const lines = csvData.split('\n').map(line => line.trim()).filter(line => line)
    let success = 0
    let failed = 0
    const errors: Array<{ line: number; phoneNumber: string; error: string }> = []

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      const lineNumber = i + 1

      // Skip header line
      if (i === 0 && (line.toLowerCase().includes('phone') || line.toLowerCase().includes('number'))) {
        continue
      }

      // Parse CSV line (simple comma-separated format)
      const parts = line.split(',').map(part => part.trim().replace(/"/g, ''))
      const phoneNumber = parts[0]
      const reason = parts[1] || 'Imported from CSV'

      if (!phoneNumber) {
        failed++
        errors.push({
          line: lineNumber,
          phoneNumber: '',
          error: 'Empty phone number'
        })
        continue
      }

      try {
        await this.addToBlacklist({ phoneNumber, reason }, addedBy)
        success++
      } catch (error) {
        failed++
        errors.push({
          line: lineNumber,
          phoneNumber,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return { success, failed, errors }
  }

  /**
   * Export blacklist to CSV format
   */
  static async exportToCSV(): Promise<string> {
    const entries = await prisma.sMSBlacklist.findMany({
      include: {
        addedByUser: {
          select: {
            firstName: true,
            lastName: true,
            username: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const csvLines = [
      'Phone Number,Reason,Added By,Date Added'
    ]

    entries.forEach(entry => {
      const addedByName = entry.addedByUser 
        ? `${entry.addedByUser.firstName} ${entry.addedByUser.lastName}`
        : 'System'
      
      csvLines.push([
        entry.phoneNumber,
        entry.reason || '',
        addedByName,
        entry.createdAt.toISOString().split('T')[0]
      ].map(field => `"${field}"`).join(','))
    })

    return csvLines.join('\n')
  }

  /**
   * Clean up old blacklist entries
   */
  static async cleanupOldEntries(olderThanDays = 365): Promise<number> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    const deleteResult = await prisma.sMSBlacklist.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate
        },
        reason: {
          not: 'Permanent block' // Don't delete permanent blocks
        }
      }
    })

    return deleteResult.count
  }

  /**
   * Get blacklist statistics
   */
  static async getBlacklistStats(): Promise<{
    total: number
    addedToday: number
    addedThisWeek: number
    addedThisMonth: number
    topReasons: Array<{ reason: string; count: number }>
  }> {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

    const [total, addedToday, addedThisWeek, addedThisMonth, reasonStats] = await Promise.all([
      prisma.sMSBlacklist.count(),
      prisma.sMSBlacklist.count({
        where: { createdAt: { gte: today } }
      }),
      prisma.sMSBlacklist.count({
        where: { createdAt: { gte: weekAgo } }
      }),
      prisma.sMSBlacklist.count({
        where: { createdAt: { gte: monthAgo } }
      }),
      prisma.sMSBlacklist.groupBy({
        by: ['reason'],
        _count: { reason: true },
        orderBy: { _count: { reason: 'desc' } },
        take: 10
      })
    ])

    const topReasons = reasonStats.map(stat => ({
      reason: stat.reason || 'No reason provided',
      count: stat._count.reason
    }))

    return {
      total,
      addedToday,
      addedThisWeek,
      addedThisMonth,
      topReasons
    }
  }
}
