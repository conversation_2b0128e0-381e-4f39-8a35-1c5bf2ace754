import { prisma } from '@/lib/db'
import { SemaphoreService } from './semaphore'
import { SMSTemplateService } from './sms-template'
import { 
  SMSQueueItem, 
  SMSProcessingResult, 
  SMSMessage,
  SendSMSInput,
  SendBatchSMSInput,
  SendTemplatedSMSInput,
  BatchSMSResponse
} from '@/lib/types/sms'
import { SMSStatus, SMSType, SMSPriority } from '../../generated/prisma'

export class SMSQueueService {
  private semaphoreService: SemaphoreService
  private isProcessing = false
  private processingInterval: NodeJS.Timeout | null = null

  constructor() {
    this.semaphoreService = new SemaphoreService()
  }

  /**
   * Add SMS to queue
   */
  async addToQueue(input: SendSMSInput, userId?: string): Promise<string> {
    const smsLog = await prisma.sMSLog.create({
      data: {
        recipientNumber: input.recipientNumber,
        message: input.message,
        type: input.type || SMSType.CUSTOM,
        priority: input.priority || SMSPriority.NORMAL,
        status: SMSStatus.QUEUED,
        scheduledFor: input.scheduledFor,
        studentId: input.studentId,
        attendanceId: input.attendanceId,
        templateId: input.templateId,
        userId
      }
    })

    return smsLog.id
  }

  /**
   * Add batch SMS to queue
   */
  async addBatchToQueue(input: SendBatchSMSInput, userId?: string): Promise<BatchSMSResponse> {
    const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const results: Array<{
      recipientNumber: string
      success: boolean
      smsLogId?: string
      error?: string
    }> = []

    let successCount = 0
    let failedCount = 0

    for (const recipient of input.recipients) {
      try {
        const smsLogId = await this.addToQueue({
          recipientNumber: recipient.recipientNumber,
          message: recipient.message,
          type: input.type,
          priority: input.priority,
          scheduledFor: input.scheduledFor,
          studentId: recipient.studentId,
          attendanceId: recipient.attendanceId,
          templateId: input.templateId
        }, userId)

        // Update with batch ID
        await prisma.sMSLog.update({
          where: { id: smsLogId },
          data: { batchId }
        })

        results.push({
          recipientNumber: recipient.recipientNumber,
          success: true,
          smsLogId
        })
        successCount++

      } catch (error) {
        results.push({
          recipientNumber: recipient.recipientNumber,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        failedCount++
      }
    }

    return {
      success: successCount > 0,
      batchId,
      totalMessages: input.recipients.length,
      successCount,
      failedCount,
      results
    }
  }

  /**
   * Add templated SMS to queue
   */
  async addTemplatedToQueue(
    input: SendTemplatedSMSInput, 
    userId?: string
  ): Promise<string> {
    const template = await SMSTemplateService.getTemplate(input.templateId)
    if (!template) {
      throw new Error('Template not found')
    }

    if (!template.isActive) {
      throw new Error('Template is not active')
    }

    const message = SMSTemplateService.renderTemplate(template.content, input.variables)

    return this.addToQueue({
      recipientNumber: input.recipientNumber,
      message,
      type: input.type,
      priority: input.priority,
      scheduledFor: input.scheduledFor,
      studentId: input.studentId,
      attendanceId: input.attendanceId,
      templateId: input.templateId
    }, userId)
  }

  /**
   * Get next items from queue for processing
   */
  async getQueueItems(limit = 10): Promise<SMSQueueItem[]> {
    const now = new Date()
    
    const smsLogs = await prisma.sMSLog.findMany({
      where: {
        status: SMSStatus.QUEUED,
        OR: [
          { scheduledFor: null },
          { scheduledFor: { lte: now } }
        ]
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' }
      ],
      take: limit
    })

    return smsLogs.map(log => ({
      id: log.id,
      smsLogId: log.id,
      priority: log.priority,
      scheduledFor: log.scheduledFor,
      retryCount: log.retryCount,
      createdAt: log.createdAt
    }))
  }

  /**
   * Process a single SMS from queue
   */
  async processSMSItem(smsLogId: string): Promise<SMSProcessingResult> {
    try {
      // Get SMS log
      const smsLog = await prisma.sMSLog.findUnique({
        where: { id: smsLogId }
      })

      if (!smsLog) {
        return {
          smsLogId,
          success: false,
          error: 'SMS log not found',
          shouldRetry: false
        }
      }

      if (smsLog.status !== SMSStatus.QUEUED) {
        return {
          smsLogId,
          success: false,
          error: 'SMS is not in queue',
          shouldRetry: false
        }
      }

      // Check if recipient is blacklisted
      const isBlacklisted = await this.isPhoneBlacklisted(smsLog.recipientNumber)
      if (isBlacklisted) {
        await prisma.sMSLog.update({
          where: { id: smsLogId },
          data: {
            status: SMSStatus.FAILED,
            failedAt: new Date(),
            errorMessage: 'Recipient is blacklisted'
          }
        })

        return {
          smsLogId,
          success: false,
          error: 'Recipient is blacklisted',
          shouldRetry: false
        }
      }

      // Update status to PENDING
      await prisma.sMSLog.update({
        where: { id: smsLogId },
        data: { status: SMSStatus.PENDING }
      })

      // Send SMS via Semaphore
      const result = await this.semaphoreService.sendSMS(
        smsLog.recipientNumber,
        smsLog.message
      )

      if (result.success) {
        // Update as sent
        await prisma.sMSLog.update({
          where: { id: smsLogId },
          data: {
            status: SMSStatus.SENT,
            sentAt: new Date(),
            messageId: result.messageId,
            cost: result.cost
          }
        })

        // Update daily cost tracking
        if (result.cost) {
          await this.updateDailyCost(result.cost)
        }

        return {
          smsLogId,
          success: true,
          messageId: result.messageId,
          cost: result.cost
        }

      } else {
        // Handle failure
        const shouldRetry = smsLog.retryCount < smsLog.maxRetries && this.isRetryableError(result.error)

        if (shouldRetry) {
          // Schedule for retry
          const retryDelay = this.calculateRetryDelay(smsLog.retryCount + 1)
          const nextRetry = new Date(Date.now() + retryDelay)

          await prisma.sMSLog.update({
            where: { id: smsLogId },
            data: {
              status: SMSStatus.QUEUED,
              retryCount: smsLog.retryCount + 1,
              scheduledFor: nextRetry,
              errorMessage: result.error
            }
          })

        } else {
          // Mark as failed
          await prisma.sMSLog.update({
            where: { id: smsLogId },
            data: {
              status: SMSStatus.FAILED,
              failedAt: new Date(),
              errorMessage: result.error
            }
          })
        }

        return {
          smsLogId,
          success: false,
          error: result.error,
          shouldRetry
        }
      }

    } catch (error) {
      console.error('SMS processing error:', error)
      
      // Mark as failed
      await prisma.sMSLog.update({
        where: { id: smsLogId },
        data: {
          status: SMSStatus.FAILED,
          failedAt: new Date(),
          errorMessage: error instanceof Error ? error.message : 'Processing error'
        }
      })

      return {
        smsLogId,
        success: false,
        error: error instanceof Error ? error.message : 'Processing error',
        shouldRetry: false
      }
    }
  }

  /**
   * Start queue processing
   */
  startProcessing(intervalMs = 30000): void {
    if (this.isProcessing) {
      return
    }

    this.isProcessing = true
    this.processingInterval = setInterval(async () => {
      await this.processQueue()
    }, intervalMs)

    console.log('SMS queue processing started')
  }

  /**
   * Stop queue processing
   */
  stopProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = null
    }
    this.isProcessing = false
    console.log('SMS queue processing stopped')
  }

  /**
   * Process queue items
   */
  private async processQueue(): Promise<void> {
    try {
      const queueItems = await this.getQueueItems(5) // Process 5 at a time
      
      if (queueItems.length === 0) {
        return
      }

      console.log(`Processing ${queueItems.length} SMS items from queue`)

      // Process items with rate limiting
      for (const item of queueItems) {
        await this.processSMSItem(item.smsLogId)
        
        // Add delay between messages to respect rate limits
        await this.delay(1000) // 1 second delay
      }

    } catch (error) {
      console.error('Queue processing error:', error)
    }
  }

  /**
   * Check if phone number is blacklisted
   */
  private async isPhoneBlacklisted(phoneNumber: string): Promise<boolean> {
    const blacklistEntry = await prisma.sMSBlacklist.findUnique({
      where: { phoneNumber }
    })
    return !!blacklistEntry
  }

  /**
   * Update daily cost tracking
   */
  private async updateDailyCost(cost: number): Promise<void> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const existingCost = await prisma.sMSCost.findUnique({
      where: { date: today }
    })

    if (existingCost) {
      const newTotalSent = existingCost.totalSent + 1
      const newTotalCost = existingCost.totalCost + cost
      const newAvgCost = newTotalCost / newTotalSent

      await prisma.sMSCost.update({
        where: { date: today },
        data: {
          totalSent: newTotalSent,
          totalCost: newTotalCost,
          avgCostPerSMS: newAvgCost
        }
      })
    } else {
      await prisma.sMSCost.create({
        data: {
          date: today,
          totalSent: 1,
          totalCost: cost,
          avgCostPerSMS: cost
        }
      })
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error?: string): boolean {
    if (!error) return false
    
    const retryableErrors = [
      'network error',
      'timeout',
      'rate limit',
      'server error',
      'temporary failure'
    ]

    return retryableErrors.some(retryableError => 
      error.toLowerCase().includes(retryableError)
    )
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    const baseDelay = 60000 // 1 minute
    const maxDelay = 3600000 // 1 hour
    
    const delay = Math.min(baseDelay * Math.pow(2, retryCount - 1), maxDelay)
    
    // Add some jitter
    return delay + Math.random() * 10000
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<{
    queued: number
    pending: number
    processing: number
    failed: number
    sent: number
    delivered: number
  }> {
    const stats = await prisma.sMSLog.groupBy({
      by: ['status'],
      _count: { status: true }
    })

    const result = {
      queued: 0,
      pending: 0,
      processing: 0,
      failed: 0,
      sent: 0,
      delivered: 0
    }

    stats.forEach(stat => {
      switch (stat.status) {
        case SMSStatus.QUEUED:
          result.queued = stat._count.status
          break
        case SMSStatus.PENDING:
          result.pending = stat._count.status
          break
        case SMSStatus.SENT:
          result.sent = stat._count.status
          break
        case SMSStatus.DELIVERED:
          result.delivered = stat._count.status
          break
        case SMSStatus.FAILED:
          result.failed = stat._count.status
          break
      }
    })

    return result
  }
}
