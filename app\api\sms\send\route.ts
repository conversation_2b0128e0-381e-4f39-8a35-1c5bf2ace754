import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { auditHelpers } from '@/lib/audit'
import { SMSQueueService } from '@/lib/services/sms-queue'
import { SemaphoreService } from '@/lib/services/semaphore'
import { SMSCostService } from '@/lib/services/sms-cost'
import { SMSType, SMSPriority } from '../../../../generated/prisma'

// SMS send request schema
const sendSMSSchema = z.object({
  recipientNumber: z.string().min(1, 'Recipient number is required'),
  message: z.string().min(1, 'Message is required').max(1600, 'Message too long'),
  type: z.nativeEnum(SMSType).optional(),
  priority: z.nativeEnum(SMSPriority).optional(),
  scheduledFor: z.string().datetime().optional(),
  studentId: z.string().cuid().optional(),
  attendanceId: z.string().cuid().optional(),
  templateId: z.string().cuid().optional(),
  sendImmediately: z.boolean().default(false) // Skip queue for immediate sending
})

/**
 * POST /api/sms/send
 * Send individual SMS message
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'sms-send', 30, 60 * 1000) // 30 SMS per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many SMS requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = sendSMSSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid SMS data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const data = validationResult.data

    // Validate phone number
    if (!SemaphoreService.validatePhoneNumber(data.recipientNumber)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid Philippine phone number format'
        },
        { status: 400 }
      )
    }

    // Check daily budget limit
    const budgetCheck = await SMSCostService.checkDailyBudgetLimit()
    if (budgetCheck.isExceeded) {
      return NextResponse.json(
        {
          success: false,
          error: 'Daily SMS budget limit exceeded',
          details: {
            currentSpending: budgetCheck.currentSpending,
            budgetLimit: budgetCheck.budgetLimit
          }
        },
        { status: 429 }
      )
    }

    const smsQueue = new SMSQueueService()

    if (data.sendImmediately) {
      // Send immediately without queue
      const semaphoreService = new SemaphoreService()
      const result = await semaphoreService.sendSMS(data.recipientNumber, data.message)

      if (result.success) {
        // Log the SMS
        await smsQueue.addToQueue({
          recipientNumber: data.recipientNumber,
          message: data.message,
          type: data.type,
          priority: data.priority,
          studentId: data.studentId,
          attendanceId: data.attendanceId,
          templateId: data.templateId
        }, user.id)

        // Log audit trail
        await auditHelpers.createAuditLog({
          userId: user.id,
          action: 'CREATE' as any,
          resource: 'sms',
          details: {
            recipientNumber: data.recipientNumber,
            messageLength: data.message.length,
            type: data.type,
            immediate: true
          },
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        })

        return NextResponse.json({
          success: true,
          messageId: result.messageId,
          cost: result.cost,
          message: 'SMS sent successfully'
        })
      } else {
        return NextResponse.json(
          {
            success: false,
            error: result.error || 'Failed to send SMS'
          },
          { status: 500 }
        )
      }
    } else {
      // Add to queue for processing
      const smsLogId = await smsQueue.addToQueue({
        recipientNumber: data.recipientNumber,
        message: data.message,
        type: data.type,
        priority: data.priority,
        scheduledFor: data.scheduledFor ? new Date(data.scheduledFor) : undefined,
        studentId: data.studentId,
        attendanceId: data.attendanceId,
        templateId: data.templateId
      }, user.id)

      // Log audit trail
      await auditHelpers.createAuditLog({
        userId: user.id,
        action: 'CREATE' as any,
        resource: 'sms',
        details: {
          smsLogId,
          recipientNumber: data.recipientNumber,
          messageLength: data.message.length,
          type: data.type,
          queued: true,
          scheduledFor: data.scheduledFor
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      })

      return NextResponse.json({
        success: true,
        smsLogId,
        message: data.scheduledFor 
          ? 'SMS scheduled successfully' 
          : 'SMS queued for sending'
      })
    }

  } catch (error) {
    console.error('SMS send error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send SMS.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send SMS.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send SMS.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to send SMS.' },
    { status: 405 }
  )
}
