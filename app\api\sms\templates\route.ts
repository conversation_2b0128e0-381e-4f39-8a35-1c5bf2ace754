import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getUserFromToken } from '@/lib/auth'
import { rateLimit } from '@/lib/rate-limit'
import { auditHelpers } from '@/lib/audit'
import { SMSTemplateService } from '@/lib/services/sms-template'
import { TemplateCategory } from '../../../../generated/prisma'

// Create template schema
const createTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(100, 'Name too long'),
  category: z.nativeEnum(TemplateCategory),
  subject: z.string().max(200, 'Subject too long').optional(),
  content: z.string().min(1, 'Template content is required').max(1600, 'Content too long'),
  variables: z.array(z.string()).default([]),
  description: z.string().max(500, 'Description too long').optional()
})

// Update template schema
const updateTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(100, 'Name too long').optional(),
  category: z.nativeEnum(TemplateCategory).optional(),
  subject: z.string().max(200, 'Subject too long').optional(),
  content: z.string().min(1, 'Template content is required').max(1600, 'Content too long').optional(),
  variables: z.array(z.string()).optional(),
  description: z.string().max(500, 'Description too long').optional(),
  isActive: z.boolean().optional()
})

/**
 * GET /api/sms/templates
 * Get SMS message templates
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'sms-templates', 100, 60 * 1000) // 100 requests per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse query parameters
    const url = new URL(request.url)
    const category = url.searchParams.get('category') as TemplateCategory | null
    const isActive = url.searchParams.get('isActive')
    const isSystem = url.searchParams.get('isSystem')
    const includeVariables = url.searchParams.get('includeVariables') === 'true'

    // Build filters
    const filters: any = {}
    if (category && Object.values(TemplateCategory).includes(category)) {
      filters.category = category
    }
    if (isActive !== null) {
      filters.isActive = isActive === 'true'
    }
    if (isSystem !== null) {
      filters.isSystem = isSystem === 'true'
    }

    // Get templates
    const templates = await SMSTemplateService.getTemplates(filters)

    // Include available variables if requested
    let availableVariables = null
    if (includeVariables) {
      availableVariables = SMSTemplateService.getAvailableVariables()
    }

    return NextResponse.json({
      success: true,
      templates,
      availableVariables,
      total: templates.length
    })

  } catch (error) {
    console.error('Get templates error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/sms/templates
 * Create/update SMS templates
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'sms-templates-create', 20, 60 * 1000) // 20 creates per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many template creation requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (only ADMIN and TEACHER can create templates)
    if (!['ADMIN', 'TEACHER'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions to create templates' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = createTemplateSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid template data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const data = validationResult.data

    try {
      // Create template
      const template = await SMSTemplateService.createTemplate(data, user.id)

      // Log audit trail
      await auditHelpers.createAuditLog({
        userId: user.id,
        action: 'CREATE' as any,
        resource: 'sms_template',
        details: {
          templateId: template.id,
          templateName: template.name,
          category: template.category
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      })

      return NextResponse.json({
        success: true,
        template,
        message: 'Template created successfully'
      })

    } catch (error) {
      if (error instanceof Error) {
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          { status: 400 }
        )
      }
      throw error
    }

  } catch (error) {
    console.error('Create template error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/sms/templates
 * Update existing template
 */
export async function PUT(request: NextRequest) {
  try {
    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions
    if (!['ADMIN', 'TEACHER'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions to update templates' },
        { status: 403 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      )
    }

    // Validate update data
    const validationResult = updateTemplateSchema.safeParse(updateData)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid template data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    try {
      // Update template
      const template = await SMSTemplateService.updateTemplate(id, validationResult.data, user.id)

      // Log audit trail
      await auditHelpers.createAuditLog({
        userId: user.id,
        action: 'UPDATE' as any,
        resource: 'sms_template',
        details: {
          templateId: template.id,
          templateName: template.name,
          updatedFields: Object.keys(validationResult.data)
        },
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown'
      })

      return NextResponse.json({
        success: true,
        template,
        message: 'Template updated successfully'
      })

    } catch (error) {
      if (error instanceof Error) {
        return NextResponse.json(
          {
            success: false,
            error: error.message
          },
          { status: 400 }
        )
      }
      throw error
    }

  } catch (error) {
    console.error('Update template error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve templates, POST to create, or PUT to update.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to retrieve templates, POST to create, or PUT to update.' },
    { status: 405 }
  )
}
