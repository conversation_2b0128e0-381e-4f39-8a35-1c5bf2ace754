import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  attendanceExportSchema,
  type AttendanceExportInput
} from '@/lib/validations/attendance'
import {
  attendanceDbUtils,
  attendanceStatusUtils
} from '@/lib/utils/attendance'

/**
 * GET /api/attendance/export
 * Export attendance data in various formats (CSV, Excel, PDF)
 */
export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting - more restrictive for exports
    const rateLimitResult = await rateLimit(request, 'attendance-export', 10, 60 * 1000) // 10 exports per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many export requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Parse and validate query parameters
    const url = new URL(request.url)
    const queryParams = Object.fromEntries(url.searchParams.entries())

    const validationResult = attendanceExportSchema.safeParse(queryParams)
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid export parameters',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const exportParams: AttendanceExportInput = validationResult.data

    // Build where clause for filtering
    const where: any = {
      date: {
        gte: exportParams.startDate,
        lte: exportParams.endDate
      }
    }

    // Apply filters
    if (exportParams.gradeLevel || exportParams.section || exportParams.status) {
      if (exportParams.gradeLevel || exportParams.section) {
        where.student = {}
        if (exportParams.gradeLevel) {
          where.student.gradeLevel = exportParams.gradeLevel
        }
        if (exportParams.section) {
          where.student.section = exportParams.section
        }
      }
      
      if (exportParams.status) {
        where.status = exportParams.status
      }
    }

    // Get attendance records
    const attendanceRecords = await prisma.attendance.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            studentNumber: true,
            firstName: true,
            lastName: true,
            middleName: true,
            gradeLevel: true,
            section: true,
            guardianName: true,
            guardianContact: true
          }
        },
        teacher: {
          select: {
            id: true,
            employeeNumber: true,
            firstName: true,
            lastName: true,
            middleName: true
          }
        }
      },
      orderBy: [
        { date: 'desc' },
        { student: { gradeLevel: 'asc' } },
        { student: { section: 'asc' } },
        { student: { lastName: 'asc' } },
        { student: { firstName: 'asc' } }
      ]
    })

    // Prepare export data
    const exportData = attendanceRecords.map(record => {
      const baseData: any = {}

      // Add fields based on selection or default fields
      const fieldsToInclude = exportParams.fields || [
        'date', 'studentNumber', 'studentName', 'gradeLevel', 'section',
        'status', 'timeIn', 'timeOut', 'teacherName'
      ]

      fieldsToInclude.forEach(field => {
        switch (field) {
          case 'date':
            baseData.Date = record.date.toISOString().split('T')[0]
            break
          case 'studentNumber':
            baseData['Student Number'] = record.student.studentNumber
            break
          case 'studentName':
            baseData['Student Name'] = `${record.student.firstName} ${record.student.lastName}`
            break
          case 'gradeLevel':
            baseData['Grade Level'] = record.student.gradeLevel
            break
          case 'section':
            baseData['Section'] = record.student.section
            break
          case 'status':
            baseData['Status'] = attendanceStatusUtils.getStatusDisplay(record.status)
            break
          case 'timeIn':
            baseData['Time In'] = record.timeIn ? 
              record.timeIn.toLocaleTimeString('en-PH', { hour12: true }) : ''
            break
          case 'timeOut':
            baseData['Time Out'] = record.timeOut ? 
              record.timeOut.toLocaleTimeString('en-PH', { hour12: true }) : ''
            break
          case 'remarks':
            baseData['Remarks'] = record.remarks || ''
            break
          case 'teacherName':
            baseData['Teacher'] = record.teacher ? 
              `${record.teacher.firstName} ${record.teacher.lastName}` : ''
            break
        }
      })

      // Add student details if requested
      if (exportParams.includeStudentDetails) {
        baseData['Middle Name'] = record.student.middleName || ''
        baseData['Guardian Name'] = record.student.guardianName
        baseData['Guardian Contact'] = record.student.guardianContact
      }

      return baseData
    })

    // Generate statistics if requested
    let statistics = null
    if (exportParams.includeStatistics) {
      const stats = await attendanceDbUtils.getAttendanceStats(
        exportParams.startDate,
        exportParams.endDate,
        {
          gradeLevel: exportParams.gradeLevel,
          section: exportParams.section
        }
      )

      statistics = {
        totalRecords: attendanceRecords.length,
        dateRange: {
          start: exportParams.startDate.toISOString().split('T')[0],
          end: exportParams.endDate.toISOString().split('T')[0]
        },
        summary: {
          total: stats.total,
          present: stats.present,
          absent: stats.absent,
          late: stats.late,
          excused: stats.excused,
          attendanceRate: stats.attendanceRate
        },
        filters: {
          gradeLevel: exportParams.gradeLevel,
          section: exportParams.section,
          status: exportParams.status
        }
      }
    }

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'EXPORT',
      resource: 'attendance',
      details: {
        format: exportParams.format,
        dateRange: {
          start: exportParams.startDate.toISOString(),
          end: exportParams.endDate.toISOString()
        },
        filters: {
          gradeLevel: exportParams.gradeLevel,
          section: exportParams.section,
          status: exportParams.status
        },
        recordCount: attendanceRecords.length,
        fields: exportParams.fields,
        includeStudentDetails: exportParams.includeStudentDetails,
        includeStatistics: exportParams.includeStatistics
      }
    }, request)

    // Return data based on format
    switch (exportParams.format) {
      case 'csv':
        return generateCSVResponse(exportData, statistics, exportParams)
      
      case 'excel':
        return generateExcelResponse(exportData, statistics, exportParams)
      
      case 'pdf':
        return generatePDFResponse(exportData, statistics, exportParams)
      
      default:
        return NextResponse.json({
          success: true,
          data: exportData,
          statistics,
          meta: {
            format: exportParams.format,
            recordCount: attendanceRecords.length,
            generatedAt: new Date().toISOString(),
            filters: {
              startDate: exportParams.startDate.toISOString().split('T')[0],
              endDate: exportParams.endDate.toISOString().split('T')[0],
              gradeLevel: exportParams.gradeLevel,
              section: exportParams.section,
              status: exportParams.status
            }
          }
        })
    }

  } catch (error) {
    console.error('GET /api/attendance/export error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Generate CSV response
function generateCSVResponse(data: any[], statistics: any, params: AttendanceExportInput) {
  let csvContent = ''
  
  // Add statistics header if included
  if (statistics) {
    csvContent += `Attendance Export Report\n`
    csvContent += `Generated: ${new Date().toLocaleString()}\n`
    csvContent += `Date Range: ${statistics.dateRange.start} to ${statistics.dateRange.end}\n`
    csvContent += `Total Records: ${statistics.totalRecords}\n`
    csvContent += `Attendance Rate: ${statistics.summary.attendanceRate.toFixed(2)}%\n`
    csvContent += `\n`
  }

  if (data.length > 0) {
    // Add headers
    const headers = Object.keys(data[0])
    csvContent += headers.join(',') + '\n'
    
    // Add data rows
    data.forEach(row => {
      const values = headers.map(header => {
        const value = row[header] || ''
        // Escape commas and quotes in CSV
        return typeof value === 'string' && (value.includes(',') || value.includes('"')) 
          ? `"${value.replace(/"/g, '""')}"` 
          : value
      })
      csvContent += values.join(',') + '\n'
    })
  }

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="attendance-export-${new Date().toISOString().split('T')[0]}.csv"`
    }
  })
}

// Generate Excel response (placeholder - would need a library like xlsx)
function generateExcelResponse(data: any[], statistics: any, params: AttendanceExportInput) {
  // This would require implementing Excel generation with a library like 'xlsx'
  // For now, return JSON with a note
  return NextResponse.json({
    success: true,
    message: 'Excel export functionality would be implemented with xlsx library',
    data,
    statistics,
    note: 'This would generate an actual Excel file in production'
  })
}

// Generate PDF response (placeholder - would need a library like puppeteer or jsPDF)
function generatePDFResponse(data: any[], statistics: any, params: AttendanceExportInput) {
  // This would require implementing PDF generation with a library like 'puppeteer' or 'jsPDF'
  // For now, return JSON with a note
  return NextResponse.json({
    success: true,
    message: 'PDF export functionality would be implemented with PDF generation library',
    data,
    statistics,
    note: 'This would generate an actual PDF file in production'
  })
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to export attendance data.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to export attendance data.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to export attendance data.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to export attendance data.' },
    { status: 405 }
  )
}
