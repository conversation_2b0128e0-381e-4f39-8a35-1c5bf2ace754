import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getUserFromToken } from '@/lib/auth'
import { auditHelpers } from '@/lib/audit'
import { rateLimit } from '@/lib/rate-limit'
import {
  manualAttendanceSchema,
  validateTimeRange,
  validateAttendanceDate,
  type ManualAttendanceInput
} from '@/lib/validations/attendance'
import {
  attendanceDbUtils,
  attendanceStatusUtils,
  transformUtils
} from '@/lib/utils/attendance'
import { smsUtils } from '@/lib/utils/sms'

/**
 * POST /api/attendance/manual
 * Manual attendance entry for students
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = await rateLimit(request, 'attendance-manual', 50, 60 * 1000) // 50 manual entries per minute
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many manual entry requests. Please try again later.',
          retryAfter: rateLimitResult.retryAfter
        },
        { status: 429 }
      )
    }

    // Get and validate user authentication
    const token = request.cookies.get('access-token')?.value ||
                  request.headers.get('authorization')?.replace('Bearer ', '')

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const user = await getUserFromToken(token)
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Check permissions (ADMIN, TEACHER, and STAFF can manually enter attendance)
    if (!['ADMIN', 'TEACHER', 'STAFF'].includes(user.role)) {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = manualAttendanceSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid attendance data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const attendanceData: ManualAttendanceInput = validationResult.data

    // Set default date to today if not provided
    const attendanceDate = attendanceData.date || new Date()

    // Validate attendance date
    const dateValidation = validateAttendanceDate(attendanceDate)
    if (!dateValidation.valid) {
      return NextResponse.json(
        {
          success: false,
          error: dateValidation.error
        },
        { status: 400 }
      )
    }

    // Validate time range if both timeIn and timeOut are provided
    if (attendanceData.timeIn && attendanceData.timeOut) {
      const timeValidation = validateTimeRange(attendanceData.timeIn, attendanceData.timeOut)
      if (!timeValidation.valid) {
        return NextResponse.json(
          {
            success: false,
            error: timeValidation.error
          },
          { status: 400 }
        )
      }
    }

    // Verify student exists and is active
    const student = await prisma.student.findUnique({
      where: { id: attendanceData.studentId },
      select: {
        id: true,
        studentNumber: true,
        firstName: true,
        lastName: true,
        middleName: true,
        gradeLevel: true,
        section: true,
        status: true
      }
    })

    if (!student) {
      return NextResponse.json(
        {
          success: false,
          error: 'Student not found'
        },
        { status: 404 }
      )
    }

    if (student.status !== 'ACTIVE') {
      return NextResponse.json(
        {
          success: false,
          error: 'Student is not active'
        },
        { status: 400 }
      )
    }

    // Check if attendance already exists for this student and date
    const existingAttendance = await attendanceDbUtils.checkAttendanceExists(
      attendanceData.studentId,
      attendanceDate
    )

    if (existingAttendance) {
      return NextResponse.json(
        {
          success: false,
          error: 'Attendance already recorded for this date',
          existing: {
            id: existingAttendance.id,
            status: existingAttendance.status,
            timeIn: existingAttendance.timeIn,
            timeOut: existingAttendance.timeOut
          }
        },
        { status: 409 }
      )
    }

    // Get teacher information for attendance record
    let teacherId = user.teacherProfileId
    if (!teacherId && user.role === 'TEACHER') {
      // Try to find teacher profile by user email
      const teacher = await prisma.teacher.findUnique({
        where: { email: user.email },
        select: { id: true }
      })
      teacherId = teacher?.id
    }

    // If no teacher profile found, use a default system teacher
    if (!teacherId) {
      const systemTeacher = await prisma.teacher.findFirst({
        where: { employeeNumber: 'SYSTEM' }
      })
      
      if (!systemTeacher) {
        const newSystemTeacher = await prisma.teacher.create({
          data: {
            employeeNumber: 'SYSTEM',
            firstName: 'System',
            lastName: 'Manual Entry',
            contactNumber: '00000000000',
            email: '<EMAIL>',
            subjectsHandled: JSON.stringify(['Attendance'])
          }
        })
        teacherId = newSystemTeacher.id
      } else {
        teacherId = systemTeacher.id
      }
    }

    // Create attendance record in transaction
    const attendance = await prisma.$transaction(async (tx) => {
      // Create the attendance record
      const newAttendance = await tx.attendance.create({
        data: {
          studentId: attendanceData.studentId,
          teacherId: teacherId!,
          date: attendanceDate,
          timeIn: attendanceData.timeIn,
          timeOut: attendanceData.timeOut,
          status: attendanceData.status,
          remarks: attendanceData.remarks ? 
            `Manual Entry: ${attendanceData.remarks}` :
            'Manual Entry'
        },
        include: {
          student: {
            select: {
              id: true,
              studentNumber: true,
              firstName: true,
              lastName: true,
              middleName: true,
              gradeLevel: true,
              section: true,
              status: true
            }
          },
          teacher: {
            select: {
              id: true,
              employeeNumber: true,
              firstName: true,
              lastName: true,
              middleName: true
            }
          }
        }
      })

      return newAttendance
    })

    // Send SMS notification asynchronously (don't wait for it)
    smsUtils.sendAttendanceNotification(
      attendanceData.studentId,
      attendance.id,
      attendanceData.status,
      attendanceData.timeIn
    ).catch(error => {
      console.warn('SMS notification failed:', error)
      // Log but don't fail the request
    })

    // Transform response data
    const transformedAttendance = transformUtils.transformAttendanceForResponse(attendance)

    // Log audit trail
    await auditHelpers.log({
      userId: user.id,
      action: 'CREATE',
      resource: 'attendance',
      resourceId: attendance.id,
      details: {
        method: 'manual_entry',
        studentId: attendanceData.studentId,
        studentName: `${student.firstName} ${student.lastName}`,
        studentNumber: student.studentNumber,
        status: attendanceData.status,
        date: attendanceDate.toISOString(),
        timeIn: attendanceData.timeIn?.toISOString(),
        timeOut: attendanceData.timeOut?.toISOString(),
        remarks: attendanceData.remarks
      }
    }, request)

    return NextResponse.json({
      success: true,
      data: transformedAttendance,
      message: `Manual attendance recorded successfully - ${attendanceStatusUtils.getStatusDisplay(attendanceData.status)}`,
      entry: {
        date: attendanceDate,
        status: attendanceStatusUtils.getStatusDisplay(attendanceData.status),
        statusColor: attendanceStatusUtils.getStatusColor(attendanceData.status),
        timeIn: attendanceData.timeIn,
        timeOut: attendanceData.timeOut
      }
    }, { status: 201 })

  } catch (error) {
    console.error('POST /api/attendance/manual error:', error)
    
    // Log error for debugging
    try {
      const token = request.cookies.get('access-token')?.value ||
                    request.headers.get('authorization')?.replace('Bearer ', '')
      const user = token ? await getUserFromToken(token) : null
      
      await auditHelpers.log({
        userId: user?.id,
        action: 'CREATE',
        resource: 'attendance',
        details: {
          method: 'manual_entry',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        }
      }, request)
    } catch (auditError) {
      console.error('Audit logging failed:', auditError)
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to record manual attendance'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create manual attendance entries.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create manual attendance entries.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create manual attendance entries.' },
    { status: 405 }
  )
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST to create manual attendance entries.' },
    { status: 405 }
  )
}
